// Example implementation of flowAppNodesToFrappeTasks
import { z } from 'zod/v4';
import { dateOrStringToDateTime, newSchedule, StructureObjectsWithSchedule } from './dateHelpers';
import { FlowAppEdge, FlowAppNode } from '../types/flow.types';
import { ScheduleSchema } from '../../libraries/zod-database-schemas/src';
import Gantt from '../../libs/frappe_gantt/src';
import { nodeRegistry, StructureObjectType } from '../components/nodes/nodeRegistry';
type Schedule = z.infer<typeof ScheduleSchema>;

// Define the task interface based on Frappe Gantt requirements
export interface FrappeTask extends Gantt.Task {
  id?: string; //Unique identifier
  name: string; //Display name
  start: string; // YYYY-MM-DD format
  end?: string; // YYYY-MM-DD format
  progress: number; // 0-100
  duration?: string;
  dependencies?: string | string[];
  custom_class?: string; // property that allows you to apply custom CSS classes to individual tasks within the Gantt chart, enabling you to style them differently based on specific criteria or data
  color?: string | undefined;
  color_progress?: string | undefined;
  _start?: Date;
  _end?: Date;
  _index?: number;
  //srcData?: FlowAppNode;  // remove: not needed if we will go back to react flow store for actions
}

// You'll need to adapt this based on your actual data structure
export function createFrappeTasks(nodes: FlowAppNode[], edges: FlowAppEdge[]): FrappeTask[] {
  const _nodes = structuredClone(nodes);
  const _edges = structuredClone(edges);

  return _nodes.map(node => {
    // Find dependencies from edges
    const dependencies = _edges.filter(edge => edge.target === node.id).map(edge => edge.source);
    // .join(",");
    // const dependencyIds = dependencies.split(",");
    const dependencyNodes = _nodes.filter(node => dependencies.includes(node.id));

    let validSchedule: Schedule;
    let validProgress: number;
    if ('schedule' in node.data) {
      //   validSchedule = extractValidStructureObjectScheduleDates(node.data);
      validSchedule = node.data.schedule?.filter(sched => sched.isActive === true)[0] as Schedule;
      validProgress = node.data.progress ?? 0;
    } else {
      // using dependencies, if structureObjectReference, use schedule from last scheduled node this one is dependent on
      // a structureObjectReference by definition can only have one(1) edge connected to source and one(1)
      // edge connected to target and they cannot be another reference
      const lastDependency = dependencyNodes[0]?.data as StructureObjectsWithSchedule;
      if (lastDependency && 'schedule' in lastDependency) {
        validSchedule = lastDependency.schedule?.filter(sched => sched.isActive === true)[0] as Schedule;
      } else {
        validSchedule = newSchedule();
      }
      validProgress = 0;
    }

    // Get node type and use it to find dimensions in nodeRegistry
    const nodeType = node.type as StructureObjectType;
    // The nodeRegistry must have the node type
    if (!nodeRegistry[nodeType]) {
      throw new Error(`Node type "${nodeType}" not found in nodeRegistry`);
    }
    // Get the default size from the nodeRegistry
    const fill = nodeRegistry[nodeType].defaultShapeProps.display.fill;

    return {
      id: node.id,
      name: node.data?.display.label || node.id,
      start: (
        dateOrStringToDateTime(
          validSchedule?.dates?.startDateActual ?? validSchedule?.dates?.startDateEstimated,
        ) || new Date(Date.now())
      ).toISOString(),
      end: (
        dateOrStringToDateTime(
          validSchedule?.dates?.completedDateActual ?? validSchedule?.dates?.completedDateEstimated,
        ) || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      ).toISOString(),
      progress: validProgress,
      dependencies: dependencies,
      color: fill,
      // color_progress:fill,
      //srcData: node,
    };
  });
}
