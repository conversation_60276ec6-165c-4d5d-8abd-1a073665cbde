// import {
//   AssemblyDocument,
//   AssemblyDocumentTemplate,
//   BaseStructureData,
//   InferredSchemaMapItemType,
//   SchemaMap,
//   StructureLink,
//   StructureLinkSchema,
//   StructureLinkTemplate,
//   StructureObject,
//   StructureObjectSchema,
//   StructureObjectTemplate,
//   StructureWorkDataType,
// } from '@tnt/zod-database-schemas';
// import { z } from 'zod/v4';

// type SchemaMapItemType<T extends z.AnyZodObject = z.AnyZodObject> = {
//   referenceString: string;
//   schema: T;
//   dbName: string;
//   dbId: string;
//   collectionName: string;
//   collectionId: string;
//   serverUri: string;
// };

// async function testFunction<
//   TObject extends StructureObject | StructureObjectTemplate,
//   TLink extends StructureLink | StructureLinkTemplate,
//   TAssemblyDoc extends AssemblyDocument | AssemblyDocumentTemplate,
// >(
//   rootObj: TObject, // do not modify
//   structure: BaseStructureData<TObject, TLink, TAssemblyDoc>, // clone
//   desiredOutputType: 'workData' | 'template',
// ) {
//   // new SchemaMapItemTypes
//   type objectSchemaTypes = SchemaMapItemType;
//   let newSmiO: SchemaMapItemType;

//   if (desiredOutputType === 'template') {
//     newSmiO = SchemaMap.ResourceObjects_StructureObjectTemplates;
//   } else if (desiredOutputType === 'workData') {
//     newSmiO = SchemaMap.WorkData_StructureObjects;
//   } else {
//     throw new Error('StructureTemplateFunctions: Error desiredOutputType not found');
//   }

//   // error here
//   // Type '{ [x: string]: any; }[]' is not assignable to type '{ status: string; _id: string; description: string; name: string; tags: string[]; structureRootReference: { containedById: string; containedByType: "structureObjectStart"; }; structureReference: { ...; }; ... 6 more ...; objectDetails: { ...; } | ... 3 more ... | { ...; }; }[]'.
//   // Type '{ [x: string]: any; }' is missing the following properties from type '{ status: string; _id: string; description: string; name: string; tags: string[]; structureRootReference: { containedById: string; containedByType: "structureObjectStart"; }; structureReference: { ...; }; ... 6 more ...; objectDetails: { ...; } | ... 3 more ... | { ...; }; }': status, _id, description, name, and 10 more.ts(2322)
//   const objects: StructureObject[] | StructureObjectTemplate[] = z
//     .array(newSmiO.schema)
//     .parse(structure.objects) as InferredSchemaMapItemType<typeof newSmiO>[];
// }

// // const check:boolean = true;

// //   const mydataschema1 = z.object({_id: z.string(), name: z.string(), data1:z.string()});
// //   const mydataschema2 = z.object({_id: z.string(), name: z.string(), data1:z.string()});
// //   type mydatatype = z.infer<typeof mydataschema1>;
// //   const somedata: mydatatype[] = [{_id:"id1", name:"name1", data1:"data1" }]

// //   type mySchemaHolderType = {name: string, schema: z.AnyZodObject}
// //   let myschemaHolder: mySchemaHolderType = {name:"mySchema", schema:mydataschema1 };

// //   if(check){
// //     myschemaHolder = {name:"mySchema", schema:mydataschema1 };
// //   }else{
// //     myschemaHolder = {name:"mySchema", schema:mydataschema2 };
// //   }

// //   type myparsedOut = {data: mydatatype[]};
// //   // this works
// //   const myout : myparsedOut = {data: z.array(mydataschema1).parse(somedata)}
// //   // this doesn't
// //   const myout2 : myparsedOut = {data: z.array(myschemaHolder.schema).parse(somedata)}
