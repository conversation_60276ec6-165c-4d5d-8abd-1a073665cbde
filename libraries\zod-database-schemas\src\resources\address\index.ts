import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';
import { LocationSchema } from '../location';

export const AddressSchema = z.object({
  _id: preprocessObjectId32,
  isActive: z.boolean(),
  validFrom: preprocessDate,
  validTo: preprocessDate,
  type: preprocessObjectId32,
  // phones: [{type:z.ObjectId, ref:"Phone"}],
  //postal lines are what are used to ship/mail/... will be sent to shipping company without edits
  postalLine1: z.string(),
  postalLine2: z.string(),
  postalLine3: z.string().optional().nullable().default(null),
  postalLine4: z.string().optional().nullable().default(null),
  postalLine5: z.string().optional().nullable().default(null),
  // the following lines are used for data analytics and should be filled in by user in addition to above lines
  cityTown: z.string(), //
  localMunicipality: z.string(), // county (U.S.)
  governingDistrict: z.string(), // State (U.S.), Province (Canada), Federal District (Mexico), County (U.K.), etc...
  postalArea: z.string(), // Zip (U.S.), Postal Code (Canada, Mexico), Postcode (U.K.)
  country: z.string(), //
  region: z.string(), // user defined sales region, customer region, ... can be used as N.America, Europe
  location: LocationSchema,
});

export type Address = z.infer<typeof AddressSchema>;
