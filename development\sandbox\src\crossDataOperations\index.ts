/**
 * CrossDataOperations testing, used to validate functionality of CrossDataOperations class
 * Execute with the command
 * FROM powershell
 * pnpm -F @tnt/sandbox exec tsx -r @dotenv-run/load .\src\crossDataOperations\index.ts
 * FROM Bash
 * pnpm -F @tnt/sandbox exec tsx -r @dotenv-run/load ./src/crossDataOperations/index.ts
 *
 * With a filter and a package.json script -- then all you would have to do is change the path in the package.json
 * pnpm -F @tnt/sandbox dev:test
 */
import { ZodTypeAny } from 'zod/v4';
import { SchemaMap, SchemaMapItemType } from '@tnt/zod-database-schemas';
import { baseCRUDOperations, CrossDataOperations } from '@tnt/mongo-client';
import { InventoryDbProduct } from '@tnt/zod-database-schemas';
import { inspect } from 'util';

(async () => {
  const sourceSchemaMap: SchemaMapItemType<ZodTypeAny, string> = SchemaMap.Inventory_Products;

  // get some data to work with and test
  let sourceDb = new baseCRUDOperations(sourceSchemaMap);
  // we will need to type this so that we can verify that we are using the correct fields later
  let sourceCol = (await sourceDb.getMany()) as InventoryDbProduct[];

  let mytest = new CrossDataOperations();
  const newObj = await mytest.expandDbObject(sourceCol, 10);
  const newObj2 = await mytest.expandDbObject(newObj, 10);

  // since the recursive iteration is only called one time, any values it replaces
  // with objects are not then expanded themselves. So to get all of the values
  // checked, you have to call the expandDbObject multiple times on the object.
  // if a values is checked and not found, the returned value indicates that.

  console.log(inspect(newObj));

  // console.log('.... process exiting');
  // process.exit(0); // Exit with code 0 (success)
})();
