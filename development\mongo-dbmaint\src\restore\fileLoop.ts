import * as fs from 'fs';
import path from 'path';

import { getAllSchemaEntries } from '@tnt/zod-database-schemas';
import { MongoDbBackupFileSchema } from '@tnt/zod-database-schemas';

import { insertDocsToDbCollection } from './insertDocsToDbCollection';
import { parseJSONFile } from './parseFile';

import type { SchemaMapItemType } from '@tnt/zod-database-schemas';
import type { ZodTypeAny } from 'zod/v4';

export const testLoop = (filePath: string): void => {
  console.log('filePath', filePath);
};

export const fileLoop = async (filePath: string): Promise<void> => {
  // read all files
  const files: string[] = fs.readdirSync(filePath).filter(file => path.extname(file) === '.json');
  // work with a single file
  // const files = fs.readdirSync(filePath).filter(file => path.basename(file) === 'Application.Theme.json');

  console.log('Processing File(s): ' + files.length);

  // Adding a Promise.all to the loop to make it async
  // loop through files (ForEach does not work in async)
  // is this actually working asynchronously since we ar waiting for
  // insertDocsToDbCollection to complete on each loop iteration?
  // shouldn't these be pushed into a promises array and that array wait for completion?
  await Promise.all(
    files.map(async file => {
      const backupFileJson = parseJSONFile(filePath, file);
      const backupFileParsed = MongoDbBackupFileSchema.parse(backupFileJson);
      const schemaMapKey = `${backupFileParsed.dbName}_${backupFileParsed.collectionName}`;
      const smis = getAllSchemaEntries();
      const smi: SchemaMapItemType<ZodTypeAny, string> | undefined = smis.find(
        item => item.referenceString === schemaMapKey,
      );
      if (!smi) {
        throw new Error(`❗  SchemaMap property: ${schemaMapKey} does not exist`);
      }
      const actionReturn = await insertDocsToDbCollection(
        smi,
        backupFileParsed.data,
        backupFileParsed.action,
      );
      if (actionReturn === false) {
        console.log('❗ error');
      }
    }),
  );
};
