import { StructureLinkSchema } from '../../../workData/structures/structureLink';

import type { z } from 'zod/v4';

// the link is what ties a project together, it is the connection point that everything is tied to.
// in the instance structures, they need to relate to the overall project/process they are part of as well as the container
// they are contained within

// structure links do NOT confer permissions, access, ... that is only done by iamLinks.

export const StructureLinkTemplateSchema = StructureLinkSchema.extend({}).omit({});

export type StructureLinkTemplate = z.infer<typeof StructureLinkTemplateSchema>;
