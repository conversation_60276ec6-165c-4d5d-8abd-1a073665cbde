import type { z } from 'zod/v4';
export type * from './aggregationOptions';
export type * from './cursorOptions';
export type * from './paginationOptions';
export type * from './pipelineStage';
export type * from './productFilters';
export type * from './queryOptions';

// eslint-disable-next-line @typescript-eslint/no-explicit-any  -- making a type alias for z.ZodObject<any, any, any> to AnyZodObject
export type AnyZodObject = z.ZodObject<any, any, any>;
