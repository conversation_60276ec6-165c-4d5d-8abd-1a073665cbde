import { getSmiByReferenceIdString } from '../schemaMap';

import type { ZodTypeAny } from 'zod/v4';
import type { SchemaMapItemType } from '../types/schemaMapItem';

export function getSchemaMapFromObjectId32(
  objectId: string,
): SchemaMapItemType<ZodTypeAny, string> | undefined {
  // const dbId = objectId.slice(0, 4);
  // const colId = objectId.slice(4, 8);
  // const schemaMap = SchemaMap.items.find(item => item.dbId === dbId && item.collectionId === colId);
  const schemaMap = getSmiByReferenceIdString(objectId.slice(0, 8));
  return schemaMap;
}
