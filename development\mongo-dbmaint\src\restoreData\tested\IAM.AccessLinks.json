{"dbName": "IAM", "collectionName": "AccessLinks", "schemaName": "iamconnectToFieldSchema", "availableActions": ["delete-insert", "append", "delete"], "action": "delete-insert", "indexes": [], "data": [{"_id": "IAM_ALIN", "connectFromFieldName": "<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectFromField": "ROBJCONT672bd5074a2abc656568ce5e", "predicate enums": ["IS_PART_OF", "HAS_ACCESS_TO"], "predicate": "IS_PART_OF", "connectToFieldName": "Group_Executives", "connectToField": "ROBJGROU6732728445da61a0c3ae49c2", "authorizationLevel": "READ"}, {"_id": "IAM_ALIN", "connectFromFieldName": "Contact_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectFromField": "ROBJCONT672bd5074a2abc656568ce64", "predicate enums": ["IS_PART_OF", "HAS_ACCESS_TO"], "predicate": "IS_PART_OF", "connectToFieldName": "Group_Executives", "connectToField": "ROBJGROU6732728445da61a0c3ae49c2", "authorizationLevel": "READ"}, {"_id": "IAM_ALIN", "connectFromFieldName": "Group_Executives", "connectFromField": "ROBJGROU6732728445da61a0c3ae49c2", "predicate enums": ["IS_PART_OF", "HAS_ACCESS_TO"], "predicate": "IS_PART_OF", "connectToFieldName": "Entity_MachAET", "connectToField": "ROBJENTI672cc479639c9344d7fb8ea8", "authorizationLevel": "READ"}, {"_id": "IAM_ALIN", "connectFromFieldName": "Group_TeamLead", "connectFromField": "ROBJGROU6732728445da61a0c3ae49c7", "predicate enums": ["IS_PART_OF", "HAS_ACCESS_TO"], "predicate": "IS_PART_OF", "connectToFieldName": "Entity_MachAET", "connectToField": "ROBJENTI672cc479639c9344d7fb8ea8", "authorizationLevel": "READ"}, {"_id": "IAM_ALIN", "connectFromFieldName": "Group_Drivers", "connectFromField": "ROBJGROU6732728445da61a0c3ae49cd", "predicate enums": ["IS_PART_OF", "HAS_ACCESS_TO"], "predicate": "HAS_ACCESS_TO", "connectToFieldName": "Group_Vehicles", "connectToField": "ROBJGROU6732728445da61a0c3ae49cb", "authorizationLevel": "READ"}, {"_id": "IAM_ALIN", "connectFromFieldName": "Group_Executives", "connectFromField": "ROBJGROU6732728445da61a0c3ae49c2", "predicate enums": ["IS_PART_OF", "HAS_ACCESS_TO"], "predicate": "IS_PART_OF", "connectToFieldName": "Group_Drivers", "connectToField": "ROBJGROU6732728445da61a0c3ae49cd", "authorizationLevel": "READ"}, {"_id": "IAM_ALIN", "connectFromFieldName": "Group_Drivers", "connectFromField": "ROBJGROU6732728445da61a0c3ae49cd", "predicate enums": ["IS_PART_OF", "HAS_ACCESS_TO"], "predicate": "HAS_ACCESS_TO", "connectToFieldName": "Group_Vehicles", "connectToField": "ROBJGROU6732728445da61a0c3ae49cb", "authorizationLevel": "WRITE"}, {"_id": "IAM_ALIN", "connectFromFieldName": "Group_Operators", "connectFromField": "ROBJGROU6732728445da61a0c3ae49ce", "predicate enums": ["IS_PART_OF", "HAS_ACCESS_TO"], "predicate": "HAS_ACCESS_TO", "connectToFieldName": "Group_Vehicles", "connectToField": "ROBJGROU6732728445da61a0c3ae49cb", "authorizationLevel": "WRITE"}, {"_id": "IAM_ALIN", "connectFromFieldName": "<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectFromField": "ROBJCONT6732728445da61a0c3ae49cf", "predicate enums": ["IS_PART_OF", "HAS_ACCESS_TO"], "predicate": "IS_PART_OF", "connectToFieldName": "Group_Operators", "connectToField": "ROBJGROU6732728445da61a0c3ae49ce", "authorizationLevel": "READ"}]}