// structureObjectBase.ts

import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../../common';
import { RaciHistoricalSchema } from '../../../resources/raci';

import { ScheduleSchema } from './schedulingDates';
import {
  ReferenceStructureObjectSchema,
  RootReferenceStructureObjectSchema,
} from './referenceStructureObject';

export const zFormFieldTypes = z.enum([
  'json',
  'textbox',
  'number',
  'checkbox',
  'select',
  'array',
  'object',
  'map',
  'label',
  'date',
  'unknown',
]);

// not start, not work, not decision, not end. Used as display/reporting object container for other objects
// also provides as the base object that other extend removing/adding properties as needed
export const StructureObjectBaseSchema = z.object({
  structureObjectType: z.literal('structureObjectBase'),
  // the _id here is used as the _containerId in the object's workData, resourceLogs, ...
  _id: preprocessObjectId32, // optional for creating new? or just use 0?
  // _id2: preprocessObjectId32TYPED([SchemaMap.WorkData_StructureObjects]),  //TODO M-1 implement controlled IDs

  structureRootReference: RootReferenceStructureObjectSchema, // this should be the main process/project, if this is the root, reference itself
  structureReference: ReferenceStructureObjectSchema, // this should be the holding structural object, if this is the root, reference itself

  name: z.string(),
  display: z.object({
    label: z.string(),
    description: z.string(),
    type: zFormFieldTypes.default('label'), //refers to data input form field type
    hidden: z.boolean().default(false),
    required: z.boolean().default(true),
    readOnly: z.boolean().default(true),
    position: z.object({ x: z.number(), y: z.number() }),
    schemeItem: preprocessObjectId32,
    // schemeName: z.string().refine(name => name.length < 255, {
    //   message: 'Name cannot be longer than 255 characters',
    // }),
    // schemeItem: z.string(),
  }),
  // object name for user/organization level identification
  organizationalReferenceId: z.string(),

  /*
    REVIEW recordLock -- should this have a historical version also in case the org changes it at some time?
    maybe all of these should simply have edit available only until it is used? Then it is readonly
    and a new version would have to be created? how would that be best accomplished?
    recordLock on all schemas? how to set to true in the referenced record?
    create function in baseDbOperations to extract all objectIds, then update? Or would this be in crossDataOperations?
    it has the potential to lock a number of collections when this happens. Maybe we should hold an array in memory of
    unlocked records, check against it, and update accordingly?
  
  
  REVIEW replying to your comment in code review -- not sure how to do this since we are in two locations
  ah, sorry, i mixed up wording between db and app. recordLock in this instance is referring
  to app record lock, which would be when a project/process was completed. It is referring to
  the fact that after a project/process is completed that the records making it up should be
  'locked' so that no further editing could be done to them and all of the information in them
  would be historically accurate no matter the changes in the database resources at a later date.
  As an example, looking at how I implemented RACI and RACIHistorical. Since RACI members are
  groups, the members of the groups would change over the period of process/project as employees
   came and left, and you would want that automatically tracked by the application. But once a
   process/project is completed, that actual final values of those groups should be retained for
   historical reference so you have the information of who was responsible for the project at the
    end. Organizations names/addresses and other information changes over time, ... how to lock
    the information from the time of the process/project into the database so that historical
    preservation of actual data is retained? And how important is it? Maybe I am just putting
    too much value on that information? At least some of it? Just thinking through as many things
    as I can and the issues that we often had with our ERP in previous use cases.
  
    */

  // different departments, groups, projects, paths ... may have different categories to define the status of tickets,
  // status will also be used to indicate DESELECTED! this value would indicate that the path it is on
  // was deselected by a decision prior to it
  status: preprocessObjectId32, // ROBJITYP
  // status value is givin by ROBJITYP also.
  // status: z.string().optional().nullable().default(null), // think kanban categories such debug, test, ...
  // statusValue: z.number().optional().nullable().default(null), // percent complete, ...

  // process object definitions can be access controlled through standard grouping techniques
  // in this way a builder for a specific department can only choose process objects that they
  // have been given access to through iamLinks

  // tags used for supporting templating, picking, filtering, ...
  tags: z.array(z.string()).optional().nullable().default([]),

  // once completed, structure objects are locked and no longer need to be evaluated
  isCompleted: z.boolean().default(false),
  completedTimeStamp: preprocessDate.optional().nullable().default(null),
  isSchedulingCurrent: z.boolean().optional().nullable().default(null),
  progress: z.number().optional().default(0), // 0-100 calculated or entered/updated from work entries.
  // retain changes in schedule for tracing, only the most recent (createdDate) should be valid
  schedule: z.array(ScheduleSchema).optional().nullable().default(null),

  // source and status
  sourceTemplate: preprocessObjectId32.optional().nullable().default(null),
  isTrackingSourceTemplate: z.boolean().optional().nullable().default(null), // if the template changes, should this object also change? only applicable in planning phase, may be incorporated into a child table under the template

  isHold: z.boolean().optional().nullable().default(false),
  holdOwners: z.array(preprocessObjectId32.optional().nullable().default(null)), // this should be people? or group?

  // responsibility

  // RACI is embedded into RACIHistorical when locked to provide historical context (resource structures vs workData structures )
  RACI: preprocessObjectId32.optional().nullable().default(null),
  RACIHistorical: RaciHistoricalSchema.optional().nullable().default(null),

  // Agile specific values
  // a user story is not the lowest level of work breakdown in an agile environment, tasks are.
  // Therefore a user story container will NOT have work data incorporated into it
  agileTaskName: preprocessObjectId32.optional().nullable().default(null), // ROBJITYP representing epic, story, release, ...
  agileTaskDescription: z.string().optional().nullable().default(null),
  agileTaskPoints: z.number().optional().nullable().default(null),
  agileTaskSize: z.string().optional().nullable().default(null),

  isExternal: z.boolean().optional().nullable().default(null), // ticket may be API call (External) = true, or waiting for TNT data to indicate complete (Internal) = false
  apiPreExecutionCallDefinition: preprocessObjectId32.optional().nullable().default(null), //reference ApiCallSchema
  apiPostExecutionCallDefinition: preprocessObjectId32.optional().nullable().default(null), //reference ApiCallSchema
  // Resources available should be made available through iamLinks.
});

export type StructureObjectBase = z.infer<typeof StructureObjectBaseSchema>;
