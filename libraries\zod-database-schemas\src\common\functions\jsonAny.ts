// import { JsonValue, JsonObject } from '@bufbuild/protobuf';
import { z } from 'zod/v4';
/**
 * Represents any possible JSON value:
 * - number
 * - string
 * - boolean
 * - null
 * - object (with any JSON value as property)
 * - array (with any JSON value as element)
 */
export type JsonValue = number | string | boolean | null | JsonObject | JsonValue[];

/**
 * Represents a JSON object.
 */
export type JsonObject = {
  [k: string]: JsonValue;
};

//JsonValueSchema
export const JsonValueSchema: z.ZodType<JsonValue> = z.union([
  z.string(),
  z.number(),
  z.boolean(),
  z.null(),
  z.array(z.lazy(() => JsonValueSchema)), // Arrays (including nested)
  z.record(
    z.string(),
    z.lazy(() => JsonValueSchema),
  ), // Objects with string keys
]);

// JsonObjectSchema
export const JsonObjectSchema = z.record(z.string(), JsonValueSchema);
