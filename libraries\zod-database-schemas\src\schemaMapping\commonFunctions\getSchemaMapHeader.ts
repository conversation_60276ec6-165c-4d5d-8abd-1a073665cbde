import type { ZodTypeAny } from 'zod/v4';
import type { SchemaMapItemType } from '../types/schemaMapItem';
import type { MongoDbBackupFile } from '../types/mongoDbBackupFile';

export const getEmptyMongoDbBackupFileObject = (
  schemaMapItem: SchemaMapItemType<ZodTypeAny, string>,
): MongoDbBackupFile => {
  const retHeader: MongoDbBackupFile = {
    dbName: schemaMapItem.dbName,
    collectionName: schemaMapItem.collectionName,
    schemaName: '',
    availableActions: ['delete-insert', 'append', 'delete'],
    action: 'delete-insert',
    indexes: [],
    data: [],
  };
  return retHeader;
};
