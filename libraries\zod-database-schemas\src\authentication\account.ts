import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../common';

// is this needed? Can we not simply use authUserSchema and session for all of this?
// even a service account should always have a user (responsible party) tied to it.
// that would be the contact and the entity which is part of the contact schema
//https://authjs.dev/guides/role-based-access-control
export const AuthAccountSchema = z.object({
  _id: preprocessObjectId32,
  userId: z.string(),
  provider: z.string(),
  providerAccountId: z.string(),
  refresh_token: z.string(),
  access_token: z.string(),
  expires_at: z.number(),
  token_type: z.string(),
  scope: z.string(),
  id_token: z.string(),
  session_state: z.string(),
});

export type AuthAccount = z.infer<typeof AuthAccountSchema>;
