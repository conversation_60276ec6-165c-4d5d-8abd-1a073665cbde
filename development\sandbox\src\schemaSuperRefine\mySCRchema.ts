// npx tsx .\development\sandbox\src\mySRSchema.ts

import { z } from 'zod/v4';
import { inspect } from 'util';

// https://github.com/colinhacks/zod/discussions/938

/* using refine to conditionally require a field in a schema based on another fields property
 * doesn't work if there are other fields as the refine value overrides any other issues
 */
const myStringSchema = z.object({
  foo: z.string(),
  num: z.number().min(5),

  // must be optional or parse will fail before refinement is applied
  bar: z.string().optional(),
});

type myString = z.infer<typeof myStringSchema>;

export const mySRSchema = myStringSchema.superRefine((data, ctx) => {
  if (data.foo.slice(0, 4) == 'ROBJ' && data.bar === undefined) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'ContainerLink & data.bar == undefined ',
      path: ['data.bar'],
    });
  }
  if (data.foo.slice(0, 4) !== 'ROBJ' && data.bar !== undefined) {
    delete data['bar'];
  }
});

export type mySRS = z.infer<typeof mySRSchema>;

function display(
  safeParseReturn: z.SafeParseReturnType<{ foo: string; num: number; bar: string | undefined }, mySRS>,
) {
  if (safeParseReturn.success) {
    console.log(safeParseReturn.success + ' data: ' + inspect(safeParseReturn.data));
  } else {
    console.log(false + ' data: ' + inspect(safeParseReturn.data));
    console.dirxml(
      '   issues: ' +
        inspect(safeParseReturn.error.issues, {
          showHidden: true,
          depth: 5,
          colors: true,
        }),
    );
  }
}

// display(myStringSchema.safeParse({ foo: 'ROBJ True', num: 8, bar: 'testbar' }));
// display(myStringSchema.safeParse({ foo: 'ROBJ True', num: 8 }));
// display(mySRSchema.safeParse({ foo: 'ROBJ True', num: 4, bar: 'testbar' })); // should be false, fail on num value
// display(mySRSchema.safeParse({ foo: 'ROBJ True', num: 8, bar: 'testbar' })); // should be true
// display(mySRSchema.safeParse({ foo: 'false ROBJ', num: 8 })); // should be true
// display(mySRSchema.safeParse({ foo: 'ROBJ True', num: 8 })); // false, should require bar

const omitTestRet = mySRSchema.safeParse({ foo: 'false ROBJ', num: 8, bar: 'omit' });
// display(omitTestRet); // should be true but strip out bar
console.log(omitTestRet);
