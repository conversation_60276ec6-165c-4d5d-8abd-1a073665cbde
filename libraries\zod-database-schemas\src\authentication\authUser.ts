import { z } from 'zod/v4';

import { ContactSchema } from '../resources';
import { preprocessDate } from '../common';

// import { EntitySchema } from '../resources';

//https://authjs.dev/guides/role-based-access-control
// https://authjs.dev/concepts/database-models

//https://brockherion.dev/blog/posts/creating-extendable-zod-schemas-with-refine/

export const AuthUserSchema = z.lazy(() =>
  ContactSchema.extend({
    // export const AuthUserSchema = z.object({
    //_id: preprocessObjectId,
    //name: z.string(),
    //email: z.string(),
    //
    loginId: z.string().default('email?'),
    emailVerified: z.date(),
    //image: z.string().url(),
    password_hash: z.string(),
    MFA_method: z.string(),
    MFA_identifier: z.string(),
    MFA_hash: z.string(),
    expiration: preprocessDate.optional().nullable().default(null),
    isLocked: z.boolean(),
    lockedReasonCode: z.string(),
  }),
);

export type AuthUser = z.infer<typeof AuthUserSchema>;
