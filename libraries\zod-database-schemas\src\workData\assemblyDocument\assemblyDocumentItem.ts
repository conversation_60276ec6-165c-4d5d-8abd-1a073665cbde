import { z } from 'zod/v4';

import { BomItemSchema } from './bomItem';
import { RafItemSchema } from './rafItem';
import { CheckSheetItemSchema } from './checkSheetItem';
import { CheckListItemSchema } from './checkListItem';

// create a compound index for name/version
// db.boms.createIndex(
//  { checkSheetName: 1, checkSheetVersion: 1 },
//   { unique: true }
// )

// used for BOMs/RAF/Checksheets -
// difference between?
// BOMs extract items from inventory to be used
// RAFs insert items into inventory
// checksheets are tally sheets and simply count items/procedures and document their status

// should BOMs support multiple levels? NO, a bom should be built to only apply to a single ticket,
//  which represents a sub-assembly in a project/process
// BOMs can then be aggregated in a task, job, project

export const AssemblyDocumentItemSchema = z.discriminatedUnion('assemblyDefType', [
  BomItemSchema,
  RafItemSchema,
  CheckSheetItemSchema,
  CheckListItemSchema,
]);

export type AssemblyDocumentItem = z.infer<typeof AssemblyDocumentItemSchema>;
