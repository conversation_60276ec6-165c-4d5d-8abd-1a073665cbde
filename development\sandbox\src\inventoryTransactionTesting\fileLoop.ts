import { Zod<PERSON>ypeAny } from 'zod/v4';
import * as fs from 'fs';
import path from 'path';
import { SchemaMap, SchemaMapItemType, JsonObject, getAllSchemaEntries } from '@tnt/zod-database-schemas';
import { parseJSONFile } from './parseFile';
import { MONGO_DB_URI } from '../constants';
import { InventoryWriteOperations } from '@tnt/mongo-client';
import { inspect } from 'util';
import { InventoryTransaction } from '@tnt/zod-database-schemas';

export const fileLoop = async (filePath: string) => {
  // read all files
  // const files = fs.readdirSync(filePath).filter(file => path.extname(file) === '.json');
  const files = fs
    .readdirSync(filePath)
    .filter(file => path.basename(file) === 'Inventory.Transactions.json');
  // .filter(file => path.basename(file) === 'singleTransaction.json');
  console.log('Processing File(s): ' + files.length);

  // Adding a Promise.all to the loop to make it async
  // loop through files (ForEach does not work in async)
  // is this actually working asynchronously since we are waiting for
  // insertDocsToDbCollection to complete on each loop iteration?
  // shouldn't these be pushed into a promises array and that array wait for completion?
  await Promise.all(
    files.map(async file => {
      let parsedData = parseJSONFile(filePath, file);
      const key1: string = 'dbName';
      const key2: string = 'collectionName';
      const dbName = (parsedData as JsonObject)[key1];
      const collectionName = (parsedData as JsonObject)[key2];

      // const schemaMapKey = file.replace('.json', '');
      const schemaMapKey = `${dbName}_${collectionName}`;
      console.log('schemaMapItem: ' + schemaMapKey);
      const smis = getAllSchemaEntries();
      const schemaMapItem: SchemaMapItemType<ZodTypeAny, string> | undefined = smis.find(
        item => item.referenceString === schemaMapKey,
      );
      if (!schemaMapItem) {
        throw new Error(`SchemaMap property: ${schemaMapKey} does not exist`);
      }
      const invOps = new InventoryWriteOperations();
      if (parsedData.data == null) {
        console.log('parsedData undefined, exiting');
        return;
      }

      let count = 0;
      for (const element of parsedData.data) {
        console.log('TransCount: ' + count.toString());
        count++;
        // if ((element as InventoryTransaction).transactionReference == 'true') {
        console.dirxml((element as InventoryTransaction).transactionData.transactionDataType);
        await invOps.executeTransaction(element);
        // }
      }
    }),
  );
};
