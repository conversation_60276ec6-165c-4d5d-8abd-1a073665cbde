import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../common';

// https://lldcoding.com/design-google-calendar-database-model

export const CalendarSchema = z.object({
  // Define your schema here
  id: preprocessObjectId32,
  name: z.string(),
  date: z.date(),
  events: z.array(
    z.object({
      eventId: z.string(),
      eventName: z.string(),
      eventDate: z.date(),
    }),
  ),
});

export type Calendar = z.infer<typeof CalendarSchema>;
