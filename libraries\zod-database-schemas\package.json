{"name": "@tnt/zod-database-schemas", "private": true, "version": "1.0.0", "main": "lib/index.js", "type": "module", "scripts": {"prebuild": "tsc", "lint": "eslint ."}, "devDependencies": {"@dotenv-run/load": "1.3.6", "@tnt/eslint-config": "workspace:*", "@tnt/shared-utilities": "workspace:*", "@tnt/typescript-config": "workspace:*", "@types/node": "22.7.4", "nodemon": "3.1.7", "ts-node": "10.9.2", "tsx": "4.19.2", "typescript": "^5.6.3"}, "dependencies": {"@tnt/error-logger": "workspace:*", "@tnt/protos-gen": "workspace:*", "@tnt/shared-enums": "workspace:*", "zod": "^3.25.67"}, "peerDependencies": {"@grpc/grpc-js": "1.11.1"}}