{"name": "@tnt/zod-client-schemas", "version": "0.0.0", "description": "Type Library", "main": "lib/index.js", "type": "module", "scripts": {"prebuild": "tsc", "lint": "eslint ."}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@tnt/create-env-file": "workspace:*", "@tnt/eslint-config": "workspace:*", "@tnt/shared-utilities": "workspace:*", "@tnt/typescript-config": "workspace:*", "@types/jest": "29.5.12", "jest": "29.7.0", "jest-mock": "29.7.0", "jest-xml-matcher": "1.2.0", "pino-pretty": "11.2.2", "supertest": "7.0.0", "ts-jest": "29.2.4", "ts-node": "10.9.2", "tsx": "4.19.2", "typescript": "^5.6.3"}, "dependencies": {"@tnt/zod-database-schemas": "workspace:*", "@tnt/shared-enums": "workspace:*", "zod": "^3.25.67"}}