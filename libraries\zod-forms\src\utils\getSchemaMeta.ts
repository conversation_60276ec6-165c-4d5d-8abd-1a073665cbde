import { Zod<PERSON>rray, ZodObject, ZodR<PERSON>ord, ZodType, ZodUnion, ZodRawShape } from 'zod/v4';
import { Logger } from '@tnt/error-logger';
import { FormFieldTypes } from '@tnt/zod-client-schemas/lib/types/zodFormsMeta';

import { extractMetadata } from './extractZodMeta';

import type { SchemaMeta } from '../types';
import type { ZodRawShape, ZodType } from 'zod/v4';

// runtime errors out and says getSchemaMeta has form any because it references its self?
// I am not seeing a runtime error.
export const getSchemaMeta = (schema: ZodType, parentKey = ''): SchemaMeta[] => {
  const logger = new Logger({ serviceName: '@tnt/zod-forms:getSchemaMeta' });
  logger.debug('getSchemaMeta', { schema, parentKey });

  if (schema instanceof ZodObject) {
    return Object.entries((schema as ZodObject<ZodRawShape>).shape).flatMap(([key, value]) => {
      const fullKey = parentKey ? `${parentKey}.${key}` : key;

      if (value instanceof ZodArray) {
        return {
          name: fullKey,
          meta: extractMetadata(value) ?? { type: FormFieldTypes.unknown, hidden: true },
          children: getSchemaMeta(value._def.type as ZodType, fullKey),
        };
      } else if (value instanceof ZodRecord) {
        return {
          name: fullKey,
          meta: extractMetadata(value) ?? { type: FormFieldTypes.map, hidden: true },
        };
      } else if (value instanceof ZodObject || value instanceof ZodUnion) {
        return {
          name: fullKey,
          meta: extractMetadata(value) ?? { type: FormFieldTypes.unknown, hidden: true },
          children: getSchemaMeta(value, fullKey),
        };
      } else if (value instanceof ZodType) {
        return {
          name: fullKey,
          meta: extractMetadata(value) ?? { type: FormFieldTypes.unknown, hidden: true },
        };
      } else {
        return {
          name: fullKey,
          meta: { type: FormFieldTypes.unknown, hidden: true },
        };
      }
    });
  }
  return [];
};
