import fs from 'node:fs';
import path from 'node:path';

export const createEnvFile = (envValues: Record<string, string>, filePath = path.join('.', '.env')): void => {
  const force = process.argv.includes('--force');

  if (fs.existsSync(filePath)) {
    // Retain existing .env values
    fs.readFileSync(filePath, 'utf-8')
      .split('\n')
      .map(line => {
        if (force) {
          return;
        }

        const [key, value] = line.split('=');

        if (!key) {
          return;
        }

        const envKey = key.trim();
        if (envValues[envKey] && envValues[envKey] !== value) {
          // eslint-disable-next-line no-console -- ok to console log in scripts
          console.warn(
            `Env mismatch for key ${envKey}, current value: ${value}, new value: ${envValues[envKey]}`,
          );
        }

        envValues[envKey] = value ?? '';
      }, {});
  }

  fs.writeFileSync(
    filePath,
    Object.entries(envValues)
      .map(([key, value]) => `${key}=${value}`)
      .join('\n'),
  );
};
