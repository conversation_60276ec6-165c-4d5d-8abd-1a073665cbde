import { z } from 'zod/v4';
import { generateObjectId } from '@tnt/shared-utilities';

import { ObjectId32Schema } from './objectId32';

// https://www.mongodb.com/docs/manual/core/document/#the-_id-field
// https://www.mongodb.com/docs/manual/reference/method/UUID/
// https://www.mongodb.com/docs/manual/reference/bson-types/
// https://www.geeksforgeeks.org/how-to-converting-objectid-to-string-in-mongodb/

// https://www.mongodb.com/blog/post/performance-best-practices-indexing

// Index keys that are of the BinData type are more efficiently stored in the index if:
// the binary subtype value is in the range of 0-7 or 128-135, and
// the length of the byte array is: 0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 20, 24, or 32.
// ------------ i believe that this is only true for UUID types? -------------------------------

// check input length to determine what type it is
// a length of 32 means it should be considered a valid TNT ObjectId
// a length of 8 means it should be considered a prefix for generating a valid TNT ObjectId
// any other length means it should be considered invalid
// a length of 24 means it should be considered a standard MongoDb ObjectId (invalid)

// Standalone preprocessing logic
export function getObjectId32(input: unknown): string {
  // typeguard mongod ObjectId and return as string
  // if (
  //   input !== null &&
  //   typeof input === 'object' &&
  //   input !== undefined &&
  //   'toString' in input &&
  //   typeof (input as any).toString === 'function'
  // ) {
  //   // Now safely check for constructor
  //   const obj = input as Record<string, any>;
  //   if (
  //     'constructor' in obj &&
  //     obj.constructor &&
  //     'name' in obj.constructor &&
  //     obj.constructor.name === 'ObjectId'
  //   ) {
  //     return obj.toString();
  //   }
  // }

  try {
    if (typeof input !== 'string') {
      return String(input);
    }
  } catch {
    return '';
  }

  if (input.length === 32) {
    return input;
  }
  if (input.length === 8) {
    return input + generateObjectId();
  }
  if (input === null) {
    return input;
  }
  return input;
}

export const preprocessObjectId32 = z.preprocess(getObjectId32, ObjectId32Schema);
