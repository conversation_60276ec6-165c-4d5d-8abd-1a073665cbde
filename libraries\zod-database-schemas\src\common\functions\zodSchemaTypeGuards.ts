import type { z } from 'zod/v4';

/**
 * Generic Zod type guard
 * @param obj - The object to check
 * @param schema - The Zod schema to check against
 * @returns true if the object matches the schema
 */

// z.ZodSchema's definition of output is any, must match
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- z.ZodSchema's definition of output is any, must match
export function isZodSchemaType<S extends z.ZodSchema<any>>(obj: unknown, schema: S): obj is z.infer<S> {
  return schema.safeParse(obj).success;
}

/**
 *
 * @param obj  - The object to check and cast if valid
 * @param schema  - The Zod schema to check against
 * @returns - typed object if the object matches the schema
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any  -- z.ZodSchema's definition of output is any, must match
export function asZodSchemaType<S extends z.ZodSchema<any>>(obj: unknown, schema: S): z.infer<S> | null {
  const result = schema.safeParse(obj);
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return  -- z.ZodSchema's definition is any, must match
  return result.success ? result.data : null;
}

// // examples

// const SomeZodSchema = z.object({
//     _id: z.string(),
//     status: z.string(),
//   });

//   type SomeZod = z.infer<typeof SomeZodSchema>;

// if (isZodSchemaType(obj, SomeZodSchema)) {
//     // ✅ obj is now inferred as SomeZod
//     console.log(obj._id);
//   }

// const casted = asZodSchemaType(obj, SomeZodSchema);
// if (casted) {
//   console.log(casted._id); // ✅ typed safely
// }
