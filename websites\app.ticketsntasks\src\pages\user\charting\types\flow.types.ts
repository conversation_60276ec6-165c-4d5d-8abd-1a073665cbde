// Flow-specific types
import {
  type Edge,
  type Node,
  type NodeProps,
  type NodeChang<PERSON>,
  type EdgeChang<PERSON>,
  type OnConnect,
  type HandleType,
  type Position,
  type Connection,
} from '@xyflow/react';
import { NodeRegistryKey } from '../components/nodes/nodeRegistry';
import { StructureObject, StructureObjectBase, StructureObjectBaseSchema } from '@tnt/zod-database-schemas';
import { NodeCreationProps } from '../utils/flowFactory';
import { z } from 'zod/v4';
import { FrappeTask } from '../utils/createFrappeTasks';

// types/flow.types.ts

/**
 * Define a DeepPartial type that makes all nested properties optional
 */
export type DeepPartial<T> = T extends object
  ? {
      [P in keyof T]?: DeepPartial<T[P]>;
    }
  : T;

// Utility type to ensure default values
type WithDefault<T, D> = T extends undefined ? D : T;

export type PaneDisplayMode = 'view' | 'edit';

export const StructureObjectEnterSchema = StructureObjectBaseSchema.extend({
  structureObjectType: z.literal('structureObjectEnter'),
});

export type StructureObjectEnter = z.infer<typeof StructureObjectEnterSchema>;

export interface StructureObjectExit extends Omit<StructureObjectBase, 'structureObjectType'> {
  structureObjectType: 'structureObjectExit';
  [key: string]: unknown; // Add this explicitly
}

// Define a type for the complete node used in the application
export type FlowAppNode = Node<StructureObject | StructureObjectEnter | StructureObjectExit, NodeRegistryKey>;

// Define props for custom node components
export type FlowNodeProps = NodeProps<FlowAppNode>;
export type FlowAppEdge = Edge;

export type NodeEventWithPointer<T = PointerEvent, NodeType extends Node = Node> = ({
  node,
  event,
}: {
  node: NodeType;
  event: T;
}) => void;

export interface NodeContextMenuItem {
  id: string;
  label: string;
  icon: string;
  action: () => void;
  className?: string;
}
export interface NodeContextMenuBase {
  show: boolean;
  x?: number;
  y?: number;
  items?: NodeContextMenuItem[];
  targetNode?: Node;
}
export type selectedNode = (nodeId: string | null) => void;
export type NodeContextMenu = (menu: NodeContextMenuBase | null) => void;

// Add these types for the pane context menu
export interface PaneContextMenuItem {
  id: string;
  label: string;
  icon: string;
  action: () => void;
  disabled?: boolean;
  className?: string;
}

export interface PaneContextMenuBase {
  show: boolean;
  x?: number;
  y?: number;
  items?: PaneContextMenuItem[];
}

type ParentAllowedTypes = 'structureObjectBase' | 'structureObjectStart';
type ParentStructureObject = Extract<
  StructureObject | StructureObjectEnter | StructureObjectExit,
  { structureObjectType: ParentAllowedTypes }
>;
export type ConstrainedParentNode = Node<ParentStructureObject, NodeRegistryKey>;

// View mode options for Frappe Gantt
export type GanttFrappeViewMode = 'Hour' | 'Quarter Day' | 'Half Day' | 'Day' | 'Week' | 'Month' | 'Year';

// Type definitions for flow state and actions
export type FlowSetNodes = (nodes: FlowAppNode[]) => void;
export type FlowSetEdges = (edges: FlowAppEdge[]) => void;
export type FlowAddNode = (nodeData: NodeCreationProps) => void;
export type FlowDeleteNode = (nodeId: string) => void;
export type FlowUpdateNode = (nodeId: string, updates: Partial<FlowAppNode>) => void;
export type FlowOnNodesChange = (changes: NodeChange[]) => void;
export type FlowOnEdgesChange = (changes: EdgeChange[]) => void;
export type FlowOnConnect = OnConnect;
export type FlowAddEdge = (connection: Connection) => void;
export type FlowSetSelectedNodeId = (nodeId: string | null) => void;
export type FlowSetMenu = (menu: NodeContextMenuBase | null) => void;
export type FlowSetCurrentParent = (nodeId: string | null) => void;
export type FlowToggleMode = () => void;
export type GanttFrappeSetViewMode = (viewMode: GanttFrappeViewMode) => void;
export type GanttSetGanttChartPosition = (position: GanttChartPosition) => void;
export type FlowLayoutDirection = 'TB' | 'LR' | 'BT' | 'RL';
export type GanttChartPosition = 'closed' | 'popout' | 'embedded';

// Interface for flow state and actions
export interface FlowState {
  // State
  nodes: FlowAppNode[];
  edges: FlowAppEdge[];
  filteredNodes: FlowAppNode[];
  filteredEdges: FlowAppEdge[];
  displayNodes: FlowAppNode[];
  displayEdges: FlowAppEdge[];
  tasks: FrappeTask[];
  mode: PaneDisplayMode;
  selectedNodeId: string | null;
  selectedNode: FlowAppNode | null;
  menu: NodeContextMenuBase | null;
  currentParentId: string | null;
  currentParentNode: ConstrainedParentNode | null;
  navigationHistory: string[];
  autoLayout: boolean;
  layoutDirection: FlowLayoutDirection;
  ganttFrappeViewMode: GanttFrappeViewMode;
  ganttChartPosition: GanttChartPosition;

  // Actions
  setNodes: FlowSetNodes;
  setEdges: FlowSetEdges;
  addNode: FlowAddNode;
  deleteNode: FlowDeleteNode;
  updateNode: FlowUpdateNode;
  addEdge: FlowAddEdge;
  onNodesChange: FlowOnNodesChange;
  onEdgesChange: FlowOnEdgesChange;
  onConnect: FlowOnConnect;
  toggleMode: FlowToggleMode;
  setMenu: FlowSetMenu;
  setSelectedNodeId: FlowSetSelectedNodeId;
  setCurrentParent: FlowSetCurrentParent;
  toggleAutoLayout: () => void;
  setLayoutDirection: (direction: FlowLayoutDirection) => void;
  applyLayout: () => void;
  setGanttFrappeViewMode: GanttFrappeSetViewMode;
  setGanttChartPosition: GanttSetGanttChartPosition;
  updateNodeInternals: (() => void) | undefined;
  setUpdateNodeInternals: (updater: () => void) => void;
  // Filtered-first actions
  refreshFilteredNodesEdges: (force?: boolean) => void;
  refreshDisplayNodesEdges: () => void;
  refreshFrappeTasks: () => void;

  // these should all be display nodes as these changes ONLY occur in the FLOW DISPLAY
  // onDisplayedNodesChange: (changes: NodeChange[]) => void;
  // onDisplayedEdgesChange: (changes: EdgeChange[]) => void;
  // onFilteredConnect: (connection: Connection) => void;
  // addFilteredNode: (nodeData: NodeCreationProps) => void;
  // addDisplayedEdge: (connection: Connection) => void;
  // updateFilteredNode: (nodeId: string, updates: Partial<FlowAppNode>) => void;
  // deleteFilteredNode: (nodeId: string) => void;
  // syncFilteredToMain: () => void;
}

// Props for flow event handlers - keep this consistent with the actions in FlowState
export interface FlowEventProps {
  mode: PaneDisplayMode;
  setNodes: FlowSetNodes;
  setEdges: FlowSetEdges;
  addNode: FlowAddNode;
  deleteNode: FlowDeleteNode;
  setMenu: FlowSetMenu;
  setSelectedNodeId: FlowSetSelectedNodeId;
  setCurrentParent?: FlowSetCurrentParent; // Optional for backward compatibility
}

type HandleOffset = {
  x?: WithDefault<number, 0>;
  y?: WithDefault<number, 0>;
};

// type HandleOffset = {
//   left: number;
//   right: number;
//   top: number;
//   bottom: number;
// };

export type HandleProps = {
  flowDirection: string;
  handleType: HandleType;
  position: Position;
  offset?: WithDefault<HandleOffset | undefined, { x: 0; y: 0 }>;
};

// Shape component props - simplified to only what's needed for rendering
export interface ShapeComponentProps {
  // Visual properties
  display: {
    // Content properties
    label: string;
    description: string;
    isToolbox: boolean;

    // Visual properties
    size: { width: number; height: number };
    fill: string;
    stroke: string;
    strokeWidth: string;

    // Optional styling properties
    labelStyle: {
      fontSize: number;
      fontFamily: string;
      fontWeight: string;
      color: string;
    };
    descriptionStyle: {
      fontSize: number;
      fontFamily: string;
      fontWeight: string;
      color: string;
    };
    handles: HandleProps[];
  };

  // // For selection state
  // selected: boolean;
}
