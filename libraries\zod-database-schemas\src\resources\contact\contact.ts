import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';
import { EmailSchema } from '../email';
import { PhoneSchema } from '../phone';
import { RateScheduleSchema } from '../rateSchedule';

// this is a person that has references in the application
// , they may or may not be a user
// a user is person authorized to use the application through the assigning of roles and allowing of sessions

// simply put, a user is always a contact, but a contact does not have to be a user
// so should user extend contact (see UserSchema)

export const ContactSchema = z.object({
  _id: preprocessObjectId32,
  isActive: z.boolean().default(true),
  validFrom: preprocessDate,
  validTo: preprocessDate,
  accountNum: z.string().optional().nullable().default(null),
  organization: preprocessObjectId32.optional().nullable().default(null), //EntitySchema.optional().nullable().default(null),
  department: preprocessObjectId32.optional().nullable().default(null), //EntitySchema.optional().nullable().default(null),
  team: preprocessObjectId32.optional().nullable().default(null), //EntitySchema.optional().nullable().default(null),
  nameFirst: z.string(),
  nameLast: z.string(),
  nameMiddle: z.string(),
  image: z.string().url().default('http://defaultUserIcon'),
  title: z.string().optional().nullable().default(null),
  note: z.string().optional().nullable().default(null),
  // address: z.array(preprocessObjectId32).optional().nullable().default(null), // z.array(AddressSchema).optional().nullable().default(null),
  // phone: z.array(PhoneSchema).optional().nullable().default(null),
  // email: z.array(EmailSchema),
  // rateSchedules: z.array(RateScheduleSchema).optional().nullable().default(null),

  address: z
    .array(z.lazy(() => preprocessObjectId32))
    .optional()
    .nullable()
    .default(null),
  phone: z
    .array(z.lazy(() => PhoneSchema))
    .optional()
    .nullable()
    .default(null),
  email: z.array(z.lazy(() => EmailSchema)),
  rateSchedules: z
    .array(z.lazy(() => RateScheduleSchema))
    .optional()
    .nullable()
    .default(null),

  // RBAC Profile - user?
  // userId: preprocessObjectId32.optional().nullable().default(null),
});

export type Contact = z.infer<typeof ContactSchema>;
