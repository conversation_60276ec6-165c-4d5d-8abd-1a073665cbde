{"name": "@tnt/sandbox", "version": "1.0.0", "description": "", "main": "index.js", "keywords": [], "author": "", "license": "ISC", "type": "module", "scripts": {"build": "pnpm run createEnv && tsc", "createEnv": "tsx scripts/createEnv.tsx", "dev:test": "pnpm -F @tnt/sandbox exec tsx -r @dotenv-run/load ./src/crossDataOperations/index.ts", "test": "jest"}, "dependencies": {"@bufbuild/buf": "^1.0.0", "@bufbuild/protobuf": "^2.2.0", "@bufbuild/protoc-gen-es": "^2.2.0", "@connectrpc/connect": "^2.0.0", "@tnt/zod-database-schemas": "workspace:*", "@tnt/env-loader": "workspace:*", "@tnt/error-logger": "workspace:*", "@tnt/mongo-client": "workspace:*", "@tnt/mongo-dbmaint": "workspace:*", "@tnt/shared-enums": "workspace:*", "@tnt/shared-utilities": "workspace:*", "@zitadel/node": "^3.0.2", "bcrypt": "5.1.1", "cors": "2.8.5", "express": "4.19.2", "jose": "5.6.3", "jsonwebtoken": "9.0.2", "lodash-es": "4.17.21", "pino": "9.3.2", "zod": "^3.25.67"}, "devDependencies": {"@babel/preset-typescript": "7.26.0", "@dotenv-run/load": "1.3.6", "@jest/globals": "29.7.0", "@tnt/create-env-file": "workspace:*", "@tnt/eslint-config": "workspace:*", "@tnt/jest-presets": "workspace:*", "@tnt/zod-database-schemas": "workspace:*", "@tnt/typescript-config": "workspace:*", "@types/bcrypt": "5.0.2", "@types/cors": "2.8.17", "@types/express": "4.17.21", "@types/jest": "29.5.12", "@types/jsonwebtoken": "9.0.7", "@types/lodash-es": "4.17.12", "@types/node": "22.7.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "jest": "29.7.0", "jest-mock": "29.7.0", "jest-xml-matcher": "1.2.0", "nodemon": "3.1.7", "pino-pretty": "11.2.2", "supertest": "7.0.0", "ts-jest": "29.2.4", "ts-node": "10.9.2", "tsup": "8.2.4", "tsx": "4.19.2", "typescript": "^5.6.3"}}