import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId, preprocessObjectId32 } from '../../common';
import { AuthUserSchema } from '../../authentication';

// in a bom, the following properties are used
// in an bom entry, this will need to incorporate a further step of inventory removal
export const BomItemSchema = z.object({
  assemblyDefType: z.literal('bom'),
  // items: z.array(
  // z.object({
  _id: preprocessObjectId,
  createdById: AuthUserSchema,
  itemName: z.string(),
  itemDescription: z.string(),
  itemNotes: z.string(),
  qtyValidation: z.enum(['Required', 'Requested', 'Estimated']),
  qty: z.number().default(0),
  workDataEntries: z
    .array(
      z.object({
        _id: preprocessObjectId,
        enteredBy: AuthUserSchema,
        timestamp: preprocessDate,
        used: z.number().default(0),
        scrapped: z.number().default(0),
      }),
    )
    .optional()
    .nullable()
    .default(null),
  bomSubstituteItemInventoryEntries: z
    .array(
      z.object({
        item: preprocessObjectId32,
        used: z.number().default(0),
        scrapped: z.number().default(0),
      }),
    )
    .optional()
    .nullable()
    .default(null),

  // itemStatus: preprocessObjectId32,
  // ItemAttachedResources: z.array(preprocessObjectId32),  // these should probably be attached by an IAMLink? not here?
  // }),
  // ),
});

export type BomItem = z.infer<typeof BomItemSchema>;
