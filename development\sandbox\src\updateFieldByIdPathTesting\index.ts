import { SchemaMapItemType, StructureObjectSchema } from '@tnt/zod-database-schemas';
import { SchemaMap } from '@tnt/zod-database-schemas'; // SchemaMap is ONLY valid for dbase-schemas
import { baseROperations, baseCRUDOperations } from '@tnt/mongo-client';
import { z } from 'zod/v4';
import { inspect } from 'util';

// from powershell
// pnpm -F @tnt/sandbox exec tsx .\src\dotUpdate\index.ts

type StructureObjectTemplate = z.infer<typeof StructureObjectSchema>;

(async () => {
  console.log('Starting retrieval');

  const readOps = new baseCRUDOperations(SchemaMap.ResourceObjects_StructureObjectTemplates);
  const structureObject = await readOps.getById('ROBJSOBT67e176789eeb1696ed275438');
  console.dirxml(inspect(structureObject, { depth: 20 }));

  //   const updatedStructureObject = await readOps.updateFieldByIdPath({id: 'ROBJSOBT67e176789eeb1696ed275438'}, path: ['sub1', sub1Id, 'sub2', sub2Id, 'field'], value: 'newValue', updateType: '$set'}));
})();

console.log('.... process exiting');
process.exit(0); // Exit with code 0 (success)
