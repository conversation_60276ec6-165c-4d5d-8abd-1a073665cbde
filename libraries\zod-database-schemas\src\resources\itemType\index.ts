import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../common';

// REVIEW should this be used to hold items such as GTIN, EAN, ...?
// the potential size of the collection would be very large
// but by creating compound indexes between itemGroup and itemName/itemValueNumber/itemValueString
// it would resolve all of the issues with verifying uniqueness and providing for selections

export const ItemTypeSchema = z.object({
  _id: preprocessObjectId32,
  itemGroup: z.string(),
  itemName: z.string(), // should this be unique by group
  itemDescription: z.string().optional().nullable().default(null),
  itemValueNumber: z.number().default(0), // should this be unique by group
  itemValueString: z.string().optional().nullable().default(null), // should this be unique by group
});

export type ItemType = z.infer<typeof ItemTypeSchema>;
