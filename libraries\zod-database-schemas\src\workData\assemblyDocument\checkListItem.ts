import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId } from '../../common';
import { AuthUserSchema } from '../../authentication';

// in a checklist, the following properties are used
export const CheckListItemSchema = z.object({
  assemblyDefType: z.literal('checklist'),
  _id: preprocessObjectId,
  // items: z.array(
  //   z.object({
  createdById: AuthUserSchema,
  itemName: z.string(),
  itemDescription: z.string(),
  itemNotes: z.string(),
  checkListItemEntries: z
    .object({
      _id: preprocessObjectId,
      enteredBy: AuthUserSchema,
      timestamp: preprocessDate,
      good: z.boolean().default(false),
      bad: z.boolean().default(false),
      indeterminate: z.boolean().default(false),
    })
    .optional()
    .nullable()
    .default(null),
  //   itemStatus: preprocessObjectId32,
  // ItemAttachedResources: z.array(preprocessObjectId32),  // these should probably be attached by an IAMLink? not here?
  //   }),
  // ),
});
