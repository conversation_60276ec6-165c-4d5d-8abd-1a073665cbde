// Import Zod
import { z } from 'zod/v4';

// Define a utility to convert Zod schemas to Proto strings
export function zodToProto(schemaName: string, schema: any): string {
  // https://protobuf.dev/reference/protobuf/google.protobuf/
  const fieldTypeMap: Record<string, string> = {
    string: 'string',
    number: 'double', // Default to double; adjust for your needs
    integer: 'int32',
    boolean: 'bool',
    array: 'repeated',
    object: 'message',
    effect: 'effect-ManualEditRequired',
    timestamp: 'timestamp',
    unsupported: 'unsupported-ManualEditRequired',
  };

  function parseSchema(schema: any, parentName: string): string {
    if (schema instanceof z.ZodObject) {
      const fields = Object.entries(schema.shape).map(([key, value], index) => {
        const fieldType = getFieldType(key, value);
        return `  ${fieldType} ${key} = ${index + 1};`;
      });
      return `message ${parentName} {\n${fields.join('\n')}\n}`;
    }
    // check for array
    else if (schema instanceof z.ZodArray) {
      const elementType = getFieldType('array', schema._def.type);
      return `${fieldTypeMap.array} ${elementType}`;
    }
    // unknown schema type
    {
      throw new Error('Unsupported schema type');
    }
  }

  function getFieldType(key: string, field: any): string {
    // remove the outer default value wrap
    if (field instanceof z.ZodDefault) {
      field = field._def.innerType;
    }
    // let required = true;
    // unwrap various outer classes
    while (
      field instanceof z.ZodOptional ||
      field instanceof z.ZodNullable ||
      field instanceof z.ZodReadonly
    ) {
      // required = false;
      field = field.unwrap();
    }

    if (field instanceof z.ZodString) return fieldTypeMap.string!;
    if (field instanceof z.ZodNumber) {
      if (field._def.checks.find(check => check.kind === 'int')) {
        return fieldTypeMap.integer!;
      } else {
        return fieldTypeMap.number!;
      }
    } // need to check for different number types?
    if (field instanceof z.ZodBoolean) return fieldTypeMap.boolean!;
    if (field instanceof z.ZodArray) {
      const elementType = getFieldType(key, field._def.type);
      return `${fieldTypeMap.array} ${elementType}`;
    }
    if (field instanceof z.ZodObject) {
      // const subMessageName = `${schemaName}_${Math.random().toString(36).substring(7)}`;
      const subMessageName = `${key}_ObjectType`;
      protoDefinitions.push(parseSchema(field, subMessageName));
      return subMessageName;
    }
    if (field instanceof z.ZodEffects || field._def.typeName === 'ZodEffects') {
      if (field._def.effect.type == 'preprocess') {
        if (field._def.schema instanceof z.ZodString) return fieldTypeMap.string!;
        else if (field._def.schema instanceof z.ZodDate) return fieldTypeMap.timestamp!;
        else if (key === '_id') return fieldTypeMap.string!;
        // haven't figured out the field type here
        else return fieldTypeMap.effect!;
      }
      //if (field._def.effect.type == 'ZodDate') return fieldTypeMap.timestamp!;
    }

    const mystring = (typeof field).toString();
    // console.log((typeof field).toString());
    return fieldTypeMap.unsupported!;
  }

  const protoDefinitions: string[] = [];
  protoDefinitions.push(parseSchema(schema, schemaName));
  return protoDefinitions.join('\n\n');
}

// // Example usage
// const GroupHistoricalSchema = z.object({
//   name: z.string(),
//   description: z.string(),
//   accountNum: z.string(),
//   tags: z.array(z.string()),
//   members: z.array(z.object({ name: z.string(), email: z.string() })),
// });

// const proto = zodToProto('GroupHistorical', GroupHistoricalSchema);
// console.log(proto);
