import fs from 'fs';
import path from 'path';

import { baseCRUDOperations } from '@tnt/mongo-client';
import { getEmptyMongoDbBackupFileObject } from '@tnt/zod-database-schemas';

import type { ZodTypeAny } from 'zod/v4';
import type { SchemaMapItemType } from '@tnt/zod-database-schemas';

// is for-of blocking?

// Asynchronous Operations:
// If your callback function contains asynchronous operations (like API calls, file reads, etc.),
// the forEach loop itself will still execute synchronously.
// However, the asynchronous operations will continue to run in the background.

export const backupCollection = async (
  schemaMapItem: SchemaMapItemType<ZodTypeAny, string>,
  directory: string,
  // ): Promise<[] | boolean> => {
): Promise<void> => {
  // not a database schema
  if (schemaMapItem.dbId === '') {
    return;
  }
  const thisCollection = new baseCRUDOperations(schemaMapItem);

  // create header for file
  const fileData = getEmptyMongoDbBackupFileObject(schemaMapItem);
  // get data from database/collection
  const data = await thisCollection.getMany();
  // should we parse here?
  const _parsedData = thisCollection.safeParse(data);

  fileData.data = data;
  // console.dirxml(inspect(fileData, { showHidden: true, depth: 5, colors: true }));
  // Convert the object to a JSON string
  const fileJSONString = JSON.stringify(fileData, null, 2);
  // get/create the file path and filename
  const filename = './' + schemaMapItem.referenceString.replace('.', '_') + '.json';
  const filePath = path.join(directory, filename);
  // Write the JSON string to a file
  fs.writeFileSync(filePath, fileJSONString);
  // return true; // should this return number of records?
};
