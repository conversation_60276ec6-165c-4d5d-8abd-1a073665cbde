import { z } from 'zod/v4';

import { AssemblyDocumentSchema } from '../../../workData';

import { AssemblyDocumentItemTemplateSchema } from './assemblyDocumentItemTemplate';

export const AssemblyDocumentTemplateSchema = AssemblyDocumentSchema.extend({
  validations: z.object({
    checkedBy: z.any().transform(() => null),
    checkedDate: z.any().transform(() => null),
    approvedBy: z.any().transform(() => null),
    approvedDate: z.any().transform(() => null),
    documentStatus: z.any().transform(() => z.enum(['Not-Started'])),
  }),
  items: z
    .array(AssemblyDocumentItemTemplateSchema)
    .refine(
      items => items.length === 0 || items.every(i => i.assemblyDefType === items[0]?.assemblyDefType),
      {
        message: 'AssemblyDocumentTemplateSchema.items - All items must have the same Assembly Document Type',
      },
    ),
});

export type AssemblyDocumentTemplate = z.infer<typeof AssemblyDocumentTemplateSchema>;
