// // @ts-expect-error no types
import js from '@eslint/js';

// // @ts-expect-error no types
import prettier from 'eslint-config-prettier';

import { localRulesPlugin } from '@tnt/eslint-config/local-rules';
// @ts-expect-error no types
import eslintPluginOnlyWarn from 'eslint-plugin-only-warn';

// ****************************************************************
// testing of legacy plugins
import { legacyPluginPlus } from './legacySupportPlus';

const eslintImportPlugin = legacyPluginPlus('eslint-plugin-import', 'import');
const eslintPromisePlugin = legacyPluginPlus('eslint-plugin-promise', 'promise');

// there is an active fork!  https://github.com/eslint-community/eslint-plugin-eslint-comments
const eslintCommentsPlugin = legacyPluginPlus('eslint-plugin-eslint-comments', 'eslint-comments');

const baseJsConfig = [
  // 🌐 Global ignore block — these will apply to every config object
  {
    ignores: [
      '**/node_modules/**',
      '**/dist/**',
      '**/lib/**',
      '**/build/**',
      '**/bin/**',
      '**/scripts/**',
      '**/eslint.config.ts',
    ],
  },
  js.configs.recommended,
  ...eslintPromisePlugin.recommended,
  ...eslintCommentsPlugin.recommended,
  prettier,
  {
    // Explicit plugin registration
    plugins: {
      'only-warn': eslintPluginOnlyWarn,
      'import': eslintImportPlugin.plugin,
      'promiseOverrides': eslintPromisePlugin.plugin,
      ...(localRulesPlugin && { 'local-rules': localRulesPlugin }),
      'eslint-commentsOverrides': eslintCommentsPlugin.plugin,
    },
    rules: {
      // Add or override rules here as needed
      'eqeqeq': 'error',
      'guard-for-in': 'error',
      //https://dev.to/diballesteros/how-to-quickly-configure-eslint-for-import-sorting-2h73
      'sort-imports': [
        'error',
        {
          ignoreCase: true,
          ignoreDeclarationSort: true,
        },
      ],
      // 'import/no-duplicates': ['error', { 'prefer-inline': false }],  // typescript already handles this. I believe it would need to be moved to a file specific configuration if we wanted to support JS
      'import/order': [
        'warn',
        {
          'groups': ['builtin', 'external', 'internal', 'parent', 'sibling', 'index', 'object', 'type'],
          'newlines-between': 'always',
        },
      ],
      'no-bitwise': 'warn',
      'no-caller': 'error',
      'no-console': 'warn',
      'no-eval': 'warn',
      'no-extend-native': 'error',
      'no-extra-label': 'warn',
      'no-implied-eval': 'error',
      'no-label-var': 'error',
      'no-lone-blocks': 'warn',
      'no-multi-str': 'error',
      'no-new': 'warn',
      'no-new-func': 'error',
      'no-object-constructor': 'error',
      'no-new-wrappers': 'warn',
      'no-octal-escape': 'error',
      'no-return-assign': 'error',
      'no-script-url': 'warn',
      'no-self-compare': 'error',
      'no-self-assign': 'warn', // replaces plugin  -- https://eslint.org/docs/latest/rules/no-useless-assignment
      'no-sequences': 'error',
      'no-shadow': 'error',
      'no-throw-literal': 'error',
      'no-unmodified-loop-condition': 'warn',
      'no-unused-expressions': 'warn',
      'no-unused-vars': ['error', { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],
      'no-useless-concat': 'warn',
      'no-var': 'warn',
      'no-void': 'error',
      'no-useless-assignment': 'warn',
      'require-atomic-updates': 'error',
      'strict': ['error', 'never'],
      // Looks like there's an official version coming, but it's not in a release yet: https://github.com/eslint/eslint/pull/17625
      // when we upgrade eslint to 9.x we can use this instead
      'local-rules/require-todo-ticketid': 'error',
      'promiseOverrides/no-nesting': 'warn', // overrides or extras
      'eslint-commentsOverrides/require-description': 'error',
      'eslint-commentsOverrides/no-unused-disable': 'error',
      'eslint-commentsOverrides/no-use': [
        'error',
        { allow: ['eslint-disable-line', 'eslint-disable-next-line'] },
      ],
    },
  },
];

// console.log(`🧪 -------- baseConfig`);
// console.log(util.inspect(baseConfig, { showHidden: false, depth: 2, colors: true }));

export default baseJsConfig;
