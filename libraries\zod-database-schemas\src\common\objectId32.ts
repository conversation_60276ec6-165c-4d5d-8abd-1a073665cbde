import { z } from 'zod/v4';

// https://www.mongodb.com/docs/manual/core/document/#the-_id-field
// https://www.mongodb.com/docs/manual/reference/method/UUID/
// https://www.mongodb.com/docs/manual/reference/bson-types/
// https://www.geeksforgeeks.org/how-to-converting-objectid-to-string-in-mongodb/

// https://www.mongodb.com/blog/post/performance-best-practices-indexing

// Index keys that are of the BinData type are more efficiently stored in the index if:
// the binary subtype value is in the range of 0-7 or 128-135, and
// the length of the byte array is: 0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 20, 24, or 32.
// ------------ i believe that this is only true for BinData types? -------------------------------

// check input length to determine what type it is
// a length of 32 means it should be considered a valid TNT ObjectId
// a length of 8 means it should be considered a prefix for generating a valid TNT ObjectId
// any other length means it should be considered invalid
// a length of 24 means it should be considered a standard MongoDb ObjectId (invalid)

// How to store this value as a BinData type? is that preferable?
// https://www.mongodb.com/docs/v7.1/reference/method/BinData/

export const ObjectId32Schema = z.string().length(32);
// export const ObjectId32Schema = preprocessObjectId32;
export type ObjectId32 = z.infer<typeof ObjectId32Schema>;
