import { z } from 'zod/v4';

import { StructureObjectStartTemplateSchema } from './subSchemas/startTemplate';
import { StructureObjectEndTemplateSchema } from './subSchemas/endTemplate';
import { StructureObjectDecisionTemplateSchema } from './subSchemas/decisionTemplate';
import { StructureObjectWorkTemplateSchema } from './subSchemas/workTemplate';
import { StructureObjectBaseTemplateSchema } from './subSchemas/baseTemplate';

// StructureObjectBaseTemplate utilizes a union to represents objects such as Start/End/Container/Decision/IO/Path
// with their specific object type data
// use extend and overwrite the object property in the base schema with the template versions while
// ommiting those we don't want

export const StructureObjectTemplateSchema = z.discriminatedUnion('structureObjectType', [
  StructureObjectBaseTemplateSchema,
  StructureObjectStartTemplateSchema,
  StructureObjectEndTemplateSchema,
  StructureObjectDecisionTemplateSchema,
  StructureObjectWorkTemplateSchema,
]);

export type StructureObjectTemplate = z.infer<typeof StructureObjectTemplateSchema>;
