import { z } from 'zod/v4';
import { CONTENT_PATH_TYPES_ALLOWED } from '@tnt/shared-enums';

import { preprocessObjectId32 } from '../../../common';

// database schema to hold all the different schemes for projects, processes, ...
// essentially templates for Project management container types
// example for waterfall (TNT styled -- Portfolio/Project/Job/Task/Ticket)
// examples for Agile (/Release/Iteration/Sprint/UserStory)
// https://www.atlassian.com/agile/project-management/epics
// https://www.geeksforgeeks.org/what-is-sprint-iteration-and-increment/
// organization should define names for different templates,
// then create/add different levels of workbreakdown as type ContainerPropertiesSchema
// to the array containerProperties
// the builder would then select a scheme to use to create a process/project
// that would contain the specific types of workbreakdown levels needed

export const StructureSchemeItemSchema = z.object({
  // _collectionName: z.literal('none').optional(),
  _id: preprocessObjectId32,
  scheme: z.string().default('scheme'),
  name: z.string().default('name'),
  category: z.string(),
  tags: z.array(z.string()).optional().nullable().default(null),
  //scope: // should be list of departments/orgs/projects, ... should be covered by cat
  // usedBy: z.array(objectIdFullyDescribed), //groups applicable to  -- moved into Access Control

  shape: z.string().default('roundrect'),
  image: z.string().url().default('http://image.jpg'), // image file location
  thumbnail: z.string().url().default('http://thumbnail.jpg'),
  // decision specific

  //

  // container specific
  contentAllowed: z.boolean().default(true),
  contentPathTypesAllowed: z.array(CONTENT_PATH_TYPES_ALLOWED).default(['linear']),
  resourceLogsAllowed: z.boolean().default(false),
  checkLogsAllowed: z.boolean().default(false),
  workDataAllowed: z.boolean().default(false),
  documentsAllowed: z.boolean().default(false),
  requirePrecedence: z.boolean().default(true),
  schemaMapItem: z
    .literal('ResourceObjects.StructureObjectTemplates')
    .default('ResourceObjects.StructureObjectTemplates'),
});

export type StructureSchemeItem = z.infer<typeof StructureSchemeItemSchema>;
