import { z } from 'zod/v4';
import { AUTHENTICATION_TYPE, CONNECTION_MODE, CONNECTION_TYPE } from '@tnt/shared-enums';

import { preprocessObjectId32 } from '../../common';

export const ApiHostConnectionSchema = z.object({
  _id: preprocessObjectId32.optional(),
  isActive: z.boolean(),
  connectionMode: CONNECTION_MODE,
  connectionType: CONNECTION_TYPE,
  hostAddress: z.string(),
  port: z.number(),
  defaultDatabaseName: z.string(),
  Authentication: AUTHENTICATION_TYPE,
  //ssl:SSL_TYPE,
  username: z.string(),
  password: z.string(), // TODO M-1:  encrypt password in database
  // there is more, see appsmith datasources/restful api
});

export type ApiHostConnection = z.infer<typeof ApiHostConnectionSchema>;
