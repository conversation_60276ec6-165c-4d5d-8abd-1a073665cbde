import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';
import { ItemTypeSchema } from '../itemType';

// embedded in entity, group, contact as an array item
// when a container is built and resources are assigned, the rate schedule to be used
// should be chosen.

export const RateScheduleSchema = z.object({
  _id: preprocessObjectId32,
  isActive: z.boolean().default(true),
  validFrom: preprocessDate,
  validTo: preprocessDate,
  name: z.string(),
  description: z.string(),
  tags: z.array(z.string()),
  costRate: z.number(),
  costRateBasis: ItemTypeSchema, // need standard types here, hr, min, day, quoted, ... that should also apply to the work entry
  billRate: z.number(),
  billRateBasis: ItemTypeSchema, // need standard types here, hr, min, day, quoted, ... that should also apply to the work entry
  validDays: z.enum(['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']),
  validTimeStart: z.string().time(),
  validTimeStop: z.string().time(),
  // INFO would need to install package such as date-holidays,public-holidays
  includesHolidays: z.boolean().default(true),
});

export type RateSchedule = z.infer<typeof RateScheduleSchema>;
