import { z } from 'zod/v4';
import {
  ScheduleSchema,
  StructureObjectDecision,
  StructureObjectWork,
  StructureObjectBase,
  StructureObjectStart,
  StructureObjectEnd,
  preprocessObjectId,
} from '@tnt/zod-database-schemas/src';
import { DateOrString } from '@tnt/zod-database-schemas/src/common/preprocessDate';
import { generateObjectId } from '../../libraries/shared-utilities/generateObjectId';

type Schedule = z.infer<typeof ScheduleSchema>;

export function getObjectWithLatestDate<T>(
  arr: T[],
  datePropertyPath: string, // Accepts nested keys like "dates.createdOn"
): T | undefined {
  if (!arr || arr.length === 0) {
    return undefined;
  }

  let latestObject: T | undefined;
  let latestDate: Date | null = null;

  function getNestedValue(obj: unknown, path: string): unknown {
    return path
      .split('.')
      .reduce(
        (acc, key) => (acc && typeof acc === 'object' ? (acc as Record<string, unknown>)[key] : undefined),
        obj,
      );
  }

  for (const obj of arr) {
    const dateValue = getNestedValue(obj, datePropertyPath);
    let currentDate: Date | null = null;

    if (dateValue instanceof Date) {
      currentDate = dateValue;
    } else if (typeof dateValue === 'string') {
      currentDate = new Date(dateValue);
    }

    if (currentDate && (!latestDate || currentDate > latestDate)) {
      latestDate = currentDate;
      latestObject = obj;
    }
  }

  return latestObject;
}

// Example:
// const data = {
//   user: "john",
//   events: [
//     { id: 1, createdOn: "2024-01-01" },
//     { id: 2, createdOn: "2024-01-03" }, // latest
//     { id: 3, createdOn: "2024-01-02" },
//     { id: 4, createdOn: "2024-01-03" }  // also latest
//   ]
// };

// const filtered = removeNonLatestDatesFromArray(data, "events", "createdOn");
// Result: { user: "john", events: [{ id: 2, createdOn: "2024-01-03" }, { id: 4, createdOn: "2024-01-03" }] }
export function removeNonLatestDatesFromArray<T>(
  obj: T,
  arrayPropertyPath: string, // Path to the array property like "dates" or "nested.dates"
  datePropertyPath: string, // Path to the date property within each array item like "createdOn" or "meta.timestamp"
): T {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  // Helper function to get nested value
  function getNestedValue(target: unknown, path: string): unknown {
    return path
      .split('.')
      .reduce(
        (acc, key) => (acc && typeof acc === 'object' ? (acc as Record<string, unknown>)[key] : undefined),
        target,
      );
  }

  // Helper function to set nested value
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function setNestedValue(target: any, path: string, value: unknown): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const nested = keys.reduce((acc, key) => {
      if (!acc[key] || typeof acc[key] !== 'object') {
        acc[key] = {};
      }
      return acc[key];
    }, target);
    nested[lastKey] = value;
  }

  // Create a deep copy of the object
  const result = JSON.parse(JSON.stringify(obj));

  // Get the array from the object
  const targetArray = getNestedValue(result, arrayPropertyPath);

  if (!Array.isArray(targetArray) || targetArray.length === 0) {
    return result;
  }

  // Find the latest date in the array
  let latestDate: Date | null = null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let latestItems: any[] = [];

  for (const item of targetArray) {
    const dateValue = getNestedValue(item, datePropertyPath);
    let currentDate: Date | null = null;

    if (dateValue instanceof Date) {
      currentDate = dateValue;
    } else if (typeof dateValue === 'string') {
      currentDate = new Date(dateValue);
    }

    if (currentDate && !isNaN(currentDate.getTime())) {
      if (!latestDate || currentDate > latestDate) {
        latestDate = currentDate;
        latestItems = [item];
      } else if (currentDate.getTime() === latestDate.getTime()) {
        latestItems.push(item);
      }
    }
  }

  // Set the filtered array back to the object
  setNestedValue(result, arrayPropertyPath, latestItems);

  return result;
}

export type StructureObjectsWithSchedule =
  | StructureObjectStart
  //   | StructureObjectStartTemplate
  | StructureObjectBase
  //   | StructureObjectBaseTemplate
  | StructureObjectDecision
  //   | StructureObjectDecisionTemplate
  | StructureObjectWork
  //   | StructureObjectWorkTemplate
  | StructureObjectEnd;
//   | StructureObjectEndTemplate

export function extractValidStructureObjectScheduleDates(structure: StructureObjectsWithSchedule): {
  start: Date | null;
  end: Date | null;
  schedule: Schedule | null;
} {
  if (!('schedule' in structure) || !structure.schedule) return { start: null, end: null, schedule: null };

  const schedules = structure.schedule;
  if (!schedules || schedules.length === 0) {
    return { start: null, end: null, schedule: null };
  }
  const validSchedule = getObjectWithLatestDate<Schedule>(schedules, 'createdOn');
  if (!validSchedule) return { start: null, end: null, schedule: null };
  const start =
    dateOrStringToDateTime(validSchedule.dates.startDateActual) ??
    dateOrStringToDateTime(validSchedule.dates.startDateEstimated) ??
    null;
  const end =
    dateOrStringToDateTime(validSchedule.dates.completedDateActual) ??
    dateOrStringToDateTime(validSchedule.dates.completedDateEstimated) ??
    null;

  return {
    start,
    end,
    schedule: validSchedule,
  };
}

export function dateOrStringToDateTime(dateOrString: DateOrString): Date | null {
  if (dateOrString instanceof Date) {
    return dateOrString;
  } else if (typeof dateOrString === 'string') {
    const date = new Date(dateOrString);
    if (isNaN(date.getTime())) {
      // return null;
      // throw new Error("Invalid Date String");
    }
    return date;
  } else {
    return null;
    // throw new Error("Invalid input type. Must be a Date or a String");
  }
}

export const newSchedule = (): Schedule => {
  const retSched: Schedule = {
    _id: generateObjectId(),
    createdOn: new Date(Date.now()).toISOString(),
    isActive: true,
    createdBy: {
      _id: 'ROBJCONT672bd5074a2abc656568ce65',
      isActive: true,
      validFrom: '2024-01-01T00:00:00.000Z',
      validTo: '2026-01-01T00:00:00.000Z',
      accountNum: 'myAccountNumber',
      organization: 'ROBJENTI681d59b1bae374ff8d132603',
      department: 'ROBJGROU681d59b1a05450adec577b13',
      team: 'ROBJGROU681d59b184a27e715ff8e03c',
      nameFirst: 'Michael',
      nameLast: 'McDaniel',
      nameMiddle: 'Connor',
      image: 'http://defaultUserIcon',
      title: "Kennard's Son",
      note: 'likes lambchops',
      address: ['ROBJADDR681d59b13964211b00672314'],
      phone: [
        {
          number: '************',
          type: 'ROBJITYP681d59b1d7748a3c80e96d68',
          description: 'use only after 5pm',
          isActive: true,
          validFrom: '2025-05-09T01:26:09.000',
          validTo: '2025-05-09T01:26:09.000',
        },
      ],
      email: [
        {
          email: '<EMAIL>',
          description: 'default',
          isActive: true,
          validFrom: '2025-05-09T01:26:09.000',
          validTo: '2025-05-09T01:26:09.000',
        },
      ],
      rateSchedules: null,
    },
    dates: {
      startDateEstimated: new Date(Date.now()).toISOString(),
      startDateActual: null,
      completedDateEstimated: new Date(Date.now() + 1 * 24 * 60 * 1000).toISOString(),
      completedDateActual: null,
      dueDate: null,
      durationEstimated: null,
      durationActual: null,
      earlyStart: 0,
      earlyFinish: 0,
      lateStart: 0,
      lateFinish: 0,
      lead: 0,
      lag: 0,
    },
  };
  return retSched;
};
