import { type Filter, ObjectId, type OptionalUnlessRequiredId, type UpdateFilter } from 'mongodb';

import { baseROperations } from './baseROperations';

import type { InsertManyResult, InsertOneResult } from 'mongodb';
import type { WithOptionalObjectId32 } from './baseDbOperations';
import type { ZodTypeAny } from 'zod/v4';
import type { SchemaMapItemType } from '@tnt/zod-database-schemas';
import type { ObjectId32 } from '@tnt/zod-database-schemas';

// provides basic CRUD operations
export class baseCRUDOperations<T extends { _id: ObjectId32 }> extends baseROperations<T> {
  protected async insertMany(
    items: WithOptionalObjectId32<T>[],
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<InsertManyResult<T>> {
    const col = await this.getCollection(smi);

    const result = await col.insertMany(items as OptionalUnlessRequiredId<T>[]);
    if (!result.acknowledged) {
      throw new Error('Creation Failed to complete.');
    }
    //verify
    // return this.getMany(result.insertedIds, undefined, smi);
    return result;
  }

  protected async insertOne(
    item: WithOptionalObjectId32<T>,
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<InsertOneResult<T>> {
    const col = await this.getCollection(smi);

    const result = await col.insertOne(item as OptionalUnlessRequiredId<T>);
    if (!result.acknowledged) {
      throw new Error('Creation Failed to complete.');
    }

    // return this.getById(result.insertedId, undefined, smi);
    return result;
  }

  async parseInsert(
    data: WithOptionalObjectId32<T> | WithOptionalObjectId32<T>[],
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<InsertOneResult<T> | InsertManyResult<T>> {
    // parse here to ensure data valid
    let validatedData: WithOptionalObjectId32<T> | WithOptionalObjectId32<T>[];
    const retData = this.safeParse(data, smi);
    // if (retData instanceof Error && retData.name == 'ZodError') {
    if (retData instanceof Error) {
      // const err: z.ZodError<any> = retData as z.ZodError<any>;
      // return err;
      throw retData;
    } else {
      validatedData = retData as WithOptionalObjectId32<T> | WithOptionalObjectId32<T>[];
    }

    const itemDataType = Object.prototype.toString.call(validatedData);
    if (itemDataType === '[object]' || itemDataType === '[object Object]') {
      const insertItem = validatedData as WithOptionalObjectId32<T>;
      return await this.insertOne(insertItem, smi);
    } else if (itemDataType === '[object Array]') {
      const insertItems = validatedData as WithOptionalObjectId32<T>[];
      return await this.insertMany(insertItems, smi);
    } else {
      throw Error("Error: mongo-client-insert: 'unknown item type'");
    }
  }

  async delete(
    id: ObjectId32,
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<boolean> {
    const filter = {
      _id: { $eq: id },
    } as Filter<T>;
    const col = await this.getCollection(smi);

    const result = await col.deleteOne(filter);
    return result.acknowledged;
  }

  async deleteAll(smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem): Promise<boolean> {
    const col = await this.getCollection(smi);

    const result = await col.deleteMany();
    return result.acknowledged;
  }

  async parseUpdate(
    id: ObjectId32,
    itemData: Partial<T>,
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<T> {
    // parse here to ensure data valid
    let validatedItemData: Partial<T>;
    const retData = this.safeParse(itemData, smi);
    // if (retData instanceof Error && retData.name == 'ZodError') {
    if (retData instanceof Error) {
      // const err: z.ZodError<any> = retData as z.ZodError<any>;
      throw retData;
    } else {
      validatedItemData = retData;
    }
    const result = this.updateOne(id, validatedItemData, smi);
    return result;
  }

  async updateOne(
    id: ObjectId32,
    validatedItemData: Partial<T>,
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<T> {
    const col = await this.getCollection(smi);

    const filter = {
      _id: { $eq: id },
    } as Filter<T>;

    const result = await col.findOneAndUpdate(
      filter, // Find item by ID
      { $set: validatedItemData }, // Update fields with new data
      { returnDocument: 'after' }, // Return the updated document after the update
    );
    if (!result?._id) {
      throw new Error(`Update failed: item with ID ${id} not found`);
    }
    return this.getById(id, undefined, smi);
  }

  /**
   * Can be used to modify a field, or array value, using static filtering and dot notation for path
   * @param id
   * @param path
   * @param value
   * @param mode
   * @param smi
   * @returns
   * examples
   * Set lag to 4	updateFieldByPath(id, 'object.schedule.0.dates.lag', 4)
   * Increment earlyStart by 1	updateFieldByPath(id, 'object.schedule.0.dates.earlyStart', 1, '$inc')
   * Push a new tag into the tags array	updateFieldByPath(id, 'tags', 'newTag', '$push')
   * Unset (remove) description	updateFieldByPath(id, 'description', '', '$unset')
   */
  async updateFieldByPath(
    id: ObjectId32,
    path: string,
    value: unknown,
    mode: '$set' | '$inc' | '$push' | '$unset' = '$set',
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<T> {
    const col = await this.getCollection(smi);
    const filter: Filter<T> = {
      _id: { $eq: id },
    } as Filter<T>;

    const updateOperation: UpdateFilter<T> = {
      [mode]: { [path]: value },
    } as unknown as UpdateFilter<T>; // <- same necessary cast

    await col.updateOne(filter, updateOperation);

    return this.getById(id, undefined, smi);
  }

  /**
   * Can be used to update a field, such as _id, using dynamic filtering,
   * a match functionality, includes support for push, inc, ...
   * Since we are using zod, and zod does NOT support unamed documents, all of our updates to single
   * documents would be statically filtered, we should not need this method
   * @param id rootId of the document
   * @param path array of property names and objectIds denoting path into desired field to update
   * given the following database structure:
   *
   * {
   * "_id": ObjectId("rootId"),
   * "sub1": [
   *     {
   *       "_id": ObjectId("sub1Id"),
   *       "sub2": [
   *         {
   *           "_id": ObjectId("sub2Id"),
   *           "field": "oldValue"
   *         }
   *       ]
   *     }
   *   ]
   * }
   *
   * the call to update "field": "oldValue" is
   * await baseCRUDOperations.updateNestedField({
   * id: { _id: rootId },
   * path: ['sub1', sub1Id, 'sub2', sub2Id, 'field'],
   * value: 'newValue',
   * updateType: '$set',
   * smi: SchemaMaptItemType
   * })
   *
   * @param value value to write to database
   * @param updateType type of mongoDB operation to perform
   * @param smi SchemaMapItemType
   * @returns
   */
  async updateFieldByIdPath(
    id: ObjectId32,
    path: (string | ObjectId)[],
    value: unknown,
    updateType: '$set' | '$inc' | '$push' | '$unset' = '$set',
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<T> {
    const col = await this.getCollection(smi);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- mongodb limitation, no type for arrayFilters
    const arrayFilters: any[] = [];
    const updatePathParts: string[] = [];
    const filter: Filter<T> = { _id: id } as Filter<T>;
    let arrayVarCount = 0;

    for (let i = 0; i < path.length; i++) {
      const segment = path[i];
      if (segment === undefined) {
        throw new Error(`Invalid path: segment at index ${i} is undefined.`);
      }
      if (segment instanceof ObjectId) {
        if (i === 0) {
          throw new Error('Invalid path: ObjectId cannot be at the start of the path');
        }

        const parentKey = path[i - 1] as string;
        const varName = `a${arrayVarCount++}`;
        updatePathParts.push(`${parentKey}.$[${varName}]`);
        arrayFilters.push({ [`${varName}._id`]: segment });

        // Add nested condition to filter in case it helps MongoDB optimize
        const dynamicKey = `${updatePathParts.slice(0, -1).join('.')}.${parentKey}._id`;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- mongodb limitation, no type for this filter
        (filter as Record<string, any>)[dynamicKey] = segment;
      } else {
        updatePathParts.push(segment);
      }
    }

    const updatePath = updatePathParts.join('.');

    const result = await col.findOneAndUpdate(
      filter,
      { [updateType]: { [updatePath]: value } },
      {
        arrayFilters: arrayFilters.length ? arrayFilters : undefined,
        returnDocument: 'after',
      },
    );

    if (!result?._id) {
      throw new Error(`Update failed: nested path ${path.join('.')} not found in document ${id}`);
    }

    return this.getById(id, undefined, smi);
  }
}
