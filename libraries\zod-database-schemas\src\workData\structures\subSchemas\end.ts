import { z } from 'zod/v4';

import { StructureObjectBaseSchema } from './base';
// provided to allow formal closing of project and linking of closing documents
export const StructureObjectEndSchema = StructureObjectBaseSchema.extend({
  structureObjectType: z.literal('structureObjectEnd'),
  // this should probably also have standard project management data such as Sponsor, .. available to it
  // project inventory
  // project documents, ...
});

export type StructureObjectEnd = z.infer<typeof StructureObjectEndSchema>;
