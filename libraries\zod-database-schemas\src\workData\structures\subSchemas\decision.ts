// import { preprocessObjectId32 } from 'zod-database-schemas';
import { z } from 'zod/v4';

import { StructureObjectBaseSchema } from './base';

// should contain and allow linking of information/documents/... for the entire project
export const StructureObjectDecisionSchema = StructureObjectBaseSchema.extend({
  structureObjectType: z.literal('structureObjectDecision'),
}).omit({
  isExternal: true,
  apiPreExecutionCallDefinition: true,
  apiPostExecutionCallDefinition: true,
});

export type StructureObjectDecision = z.infer<typeof StructureObjectDecisionSchema>;
