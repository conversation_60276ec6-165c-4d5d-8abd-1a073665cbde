import { Logger } from '@tnt/error-logger';
import { ThemeSchema as ThemeClientSchema } from '@tnt/zod-client-schemas';
import { SchemaMap, ThemeSchema } from '@tnt/zod-database-schemas'; // SchemaMap is ONLY valid for dbase-schemas
import { baseROperations } from '@tnt/mongo-client';

import type { GetThemeRequest } from '@tnt/protos-gen/lib/__generated__/tnt';
import type { z } from 'zod/v4';

type ThemeClient = z.infer<typeof ThemeClientSchema>;
type ThemeDatabase = z.infer<typeof ThemeSchema>;

export const getById = async (req: GetThemeRequest): Promise<ThemeClient> => {
  const logger = new Logger({ serviceName: '@tnt/application-server' });
  const { Id } = req;

  const result = ThemeSchema.shape._id.safeParse(Id);
  // https://zod.dev/ERROR_HANDLING?id=error-handling-for-forms
  if (!result.success) {
    logger.error(result.error.issues.join(`/r/n`));
    throw result.error;
  }
  const id = result.data;

  const themeOperations = new baseROperations<ThemeDatabase>(SchemaMap.Application_Theme);

  const themeDatabase = await themeOperations
    .getById(id, {
      info: {
        _id: 1,
        themeName: 1,
        layout: 1,
        overrides: 1,
        palette: 1,
        typography: 1,
        zones: 1,
      },
    })
    .catch((err: Error) => {
      logger.error(`Theme:getById: err: ${err}`);
      throw err;
    });

  return ThemeClientSchema.parse(themeDatabase);
};
