import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../common';

import { InventoryDbSKUSchema } from './sku';
import { InventoryPriceSchema } from './price';

// an inventory product may be described as an internal project type of product
// in this case, product numbers can be generated that reference the project,
// and skus generated for items under that product for different items used internally
// to that product
// after the project/process closes, these items will be orphaned and no longer available
// sums are read only as they are to only be written with a transaction

// const colorSchema = z.object({
//   name: z.string(),
//   hex: z.string(),
// });

export const InventoryDbProductSchema = z.object({
  _id: preprocessObjectId32, // base collection document, should use preprocessObjectId32!
  active: z.boolean(), // used to deactivate old products
  productName: z.string(),
  productCode: z.string(),
  productDescription: z.string(),
  tags: z.array(z.string().optional().nullable().default(null)).optional().nullable().default([]),
  accountingCrossReference: z.string().optional().nullable().default(null),
  cost: InventoryPriceSchema.optional().nullable().default(null),
  referenceDocs: z
    .array(z.object({ docType: preprocessObjectId32, docId: preprocessObjectId32 }))
    .optional()
    .nullable()
    .default(null), // this would reference drawing, images, ..., in the format {ROBJITYP, ROBJDOCU}...
  // items is an array that includes all SKUs of product, in all locations
  // with each SKU:location being unique
  // skus: z.array(InventoryDbSKUSchema).optional(),
  skus: z.array(InventoryDbSKUSchema).optional().nullable().default(null), // sku's, no location data?
  productSum: z.number().readonly().default(0),
  productReservedSum: z.number().readonly().default(0),
});

export type InventoryDbProduct = z.infer<typeof InventoryDbProductSchema>;

// to implement inventory transactions, an item is added/removed by inserting an inventory transaction into the
// transactions collection and then by adding (if applicable) serial/lot/batch numbers
// to the InventoryProduct.InventorySKU.InventoryLocationItem.itemIds array and updating the sum arrays of
// InventoryProduct.productSum
// InventoryProduct.InventorySKUSchema.itemSum
// InventoryProduct.InventorySKU.InventoryLocationItem.locationSum

// price is included at all levels to support multiple pricing structures, costs, ...
// price returned should be prioritized as InventoryLocationItem, InventorySKU, InventoryProduct
