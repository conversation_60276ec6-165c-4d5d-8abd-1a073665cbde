import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';
import { ContactSchema } from '../contact/contact';
import { LocationSchema } from '../location';

// https://lldcoding.com/design-google-calendar-database-model

export const EventSchema = z.object({
  _id: preprocessObjectId32,
  calendar_id: preprocessObjectId32,
  title: z.string(),
  description: z.string(),
  owner: z.array(ContactSchema),
  required: z.array(ContactSchema),
  notRequired: z.array(ContactSchema),
  start_time: preprocessDate,
  end_time: preprocessDate,
  location: LocationSchema,
  is_recurring: z.boolean().default(false),
  recurrence_rule: z.string(),
  created_at: preprocessDate,
  updated_at: preprocessDate,
});

export type Event = z.infer<typeof EventSchema>;
