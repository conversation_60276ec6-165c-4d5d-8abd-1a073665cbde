// /**
//  * Library designed to extract structures objects and links providing UI with graph data
//  */

import { getSmiByReferenceIdString, SchemaMap } from '@tnt/zod-database-schemas';

import { baseROperations } from '../../baseOperations/baseROperations';

import type { ZodTypeAny } from 'zod/v4';
import type { Filter } from 'mongodb';
import type {
  AssemblyDocument,
  AssemblyDocumentTemplate,
  BaseStructureData,
  iamLink,
  ObjectId32,
  SchemaMapItemType,
  StructureLink,
  StructureLinkTemplate,
  StructureObject,
  StructureObjectTemplate,
} from '@tnt/zod-database-schemas';

export class StructureReadOperations<T extends { _id: ObjectId32 }> extends baseROperations<T> {
  /**
   *
   * @param structureObjectIds typically from the ParentS
   * @returns Object containing all of the structural objects and links in the array passed
   */
  public async getStructuralContents<
    TObject extends StructureObject | StructureObjectTemplate,
    TLink extends StructureLink | StructureLinkTemplate,
    TAssemblyDoc extends AssemblyDocument | AssemblyDocumentTemplate,
  >(
    structureObject: StructureObject | StructureObjectTemplate,
    // ): Promise<StructureWorkDataType | StructureTemplateType> {
    // ): Promise<{
    //   objects: Array<StructureObject> | Array<StructureObjectTemplate>;
    //   links: Array<StructureLink> | Array<StructureLinkTemplate>;
    //   assemblyDocuments: Array<AssemblyDocument> | Array<AssemblyDocumentTemplate>;
    //   iamLinks: Array<iamLink>;
    // }> {
  ): Promise<BaseStructureData<TObject, TLink, TAssemblyDoc>> {
    let containedStructureObjects;
    // check if root or not  (references itself as containedById)
    if (structureObject._id === structureObject.structureRootReference.containedById) {
      // this should be a much faster retrieval
      containedStructureObjects = (await this.getAllContainedStructureObjectsFromRoot(
        structureObject._id,
      )) as Array<TObject>;
    } else {
      containedStructureObjects = (await this.getAllContainedStructureObjects(
        structureObject._id,
      )) as Array<TObject>;
    }

    // extract all _id's from of the structureObjects
    const containedStructureIds = containedStructureObjects.map(obj => obj._id);

    // get all structure links for the structure
    const containedLinks = (await StructureReadOperations.getStructureLinks(
      containedStructureIds,
    )) as Array<TLink>;

    // get all assembly documents
    const containedAssemblyDocuments = (await StructureReadOperations.getAssemblyDocuments(
      containedStructureIds,
    )) as Array<TAssemblyDoc>;

    // get all iam links
    const containedIamLinks = await StructureReadOperations.getIamLinks(containedStructureIds);

    return {
      objects: containedStructureObjects,
      links: containedLinks,
      assemblyDocuments: containedAssemblyDocuments,
      iamLinks: containedIamLinks,
    };
  }

  /**
   *
   * @param rootContainer parent structureObject of type with structureContents array
   * @returns an array of all containers in the rootContainer structureContents array
   *
   */
  private async getAllContainedStructureObjectsFromRoot(
    structureRootReferenceId: ObjectId32,
  ): Promise<Array<StructureObject> | Array<StructureObjectTemplate>> {
    const smi = getSmiByReferenceIdString(structureRootReferenceId.toString());
    if (!smi) throw new Error('No matching schema found, check structureObjectIds prefix');

    // used to ensure the smi collection for the objectIds that are passed is initialized
    const _myOps = this.getCollection(smi);

    const containerFilter: Filter<StructureObject> = {
      structureRootReferenceId: { $eq: structureRootReferenceId },
    };
    const result = await this.getMany(containerFilter as Filter<T>, undefined, smi);

    return result as unknown as Array<StructureObject> | Array<StructureObjectTemplate>;
  }

  /**
   * Recursively collects all ObjectIds connected to a starting object through structuralContents references
   * @param startingStructureObjectId The ID of the object to start the traversal from
   * @returns Promise with array of all connected ObjectIds
   */
  private async getAllContainedStructureObjectIds(
    startingStructureObjectId: ObjectId32,
  ): Promise<Array<string>> {
    // since this method can be used with either a ResourceObject or a WorkData object, we need to determine correct
    // database.collection to read from
    const smi = getSmiByReferenceIdString(startingStructureObjectId.toString());
    if (!smi) throw new Error('No matching schema found, check structureObjectIds prefix');

    if (
      !(
        smi.dbId === SchemaMap.WorkData_StructureObjects.dbId ||
        smi.dbId === SchemaMap.ResourceObjects_StructureObjectTemplates.dbId
      )
    ) {
      throw new Error('No matching schema found, check structureObjectIds prefix');
    }
    const collection = await this.getCollection(smi);

    // This aggregation uses $graphLookup to recursively traverse the references
    const result = await collection
      .aggregate([
        // Start with the object we're interested in
        { $match: { _id: startingStructureObjectId } },

        // Use $graphLookup to recursively follow all structuralContents references
        {
          $graphLookup: {
            from: smi.collectionName, // The collection to search within
            startWith: '$structuralContents', // Begin with the immediate references
            connectFromField: 'structuralContents', // Field to connect from
            connectToField: '_id', // Field to connect to
            as: 'connectedObjects', // Output array field
            maxDepth: 100, // Reasonable limit to prevent infinite recursion
            depthField: 'depth', // Optional: stores the depth level of each object
          },
        },

        // Restructure to get all IDs in one flat array
        {
          $project: {
            allObjectIds: {
              $concatArrays: [
                ['$_id'], // Include the starting object's ID
                '$connectedObjects._id', // Include all connected object IDs
                {
                  // Include all structuralContents from the starting object
                  $cond: {
                    if: { $isArray: '$structuralContents' },
                    then: '$structuralContents',
                    else: [],
                  },
                },
              ],
            },
          },
        },

        // Unwind to get all IDs as separate documents
        { $unwind: '$allObjectIds' },

        // Group to eliminate duplicates
        {
          $group: {
            _id: null,
            uniqueIds: { $addToSet: '$allObjectIds' },
          },
        },

        // Final projection to clean up the result
        {
          $project: {
            _id: 0,
            ids: '$uniqueIds',
          },
        },
      ])
      .toArray();

    // Return the array of IDs or empty array if no results
    return (result[0]?.ids as string[]) ?? [];
  }

  private async getAllContainedStructureObjects(
    startingStructureObjectId: ObjectId32,
  ): Promise<Array<StructureObject> | Array<StructureObjectTemplate>> {
    const smi = getSmiByReferenceIdString(startingStructureObjectId.toString());
    if (!smi) throw new Error('No matching schema found, check structureObjectIds prefix');

    if (
      !(
        smi.dbId === SchemaMap.WorkData_StructureObjects.dbId ||
        smi.dbId === SchemaMap.ResourceObjects_StructureObjectTemplates.dbId
      )
    ) {
      throw new Error('No matching schema found, check structureObjectIds prefix');
    }

    const collection = await this.getCollection(smi);

    const result = await collection
      .aggregate([
        // Match the starting document
        { $match: { _id: startingStructureObjectId } },

        // Recursively fetch all connected documents
        {
          $graphLookup: {
            from: smi.collectionName,
            startWith: '$structuralContents',
            connectFromField: 'structuralContents',
            connectToField: '_id',
            as: 'connectedObjects',
            maxDepth: 100,
            depthField: 'depth',
          },
        },

        // Add the starting object itself to the connected ones
        {
          $project: {
            allObjects: {
              $concatArrays: [
                ['$$ROOT'], // include the starting document
                '$connectedObjects', // all connected documents
              ],
            },
          },
        },

        // Unwind to flatten into individual documents
        { $unwind: '$allObjects' },

        // Replace root with each document to return full objects
        { $replaceRoot: { newRoot: '$allObjects' } },

        // Group by _id to remove duplicates
        {
          $group: {
            _id: '$_id',
            doc: { $first: '$$ROOT' }, // keep the full doc
          },
        },

        // Project only the document itself (removing `_id` wrapper key)
        {
          $replaceRoot: { newRoot: '$doc' },
        },
      ])
      .toArray();

    // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- Safe: validated by Zod, type is context-dependent
    const parsed = result.map(doc => smi.schema.parse(doc)) as unknown;
    const typedResult = parsed as Array<StructureObject> | Array<StructureObjectTemplate>;
    return typedResult;
  }

  private static async getStructureLinks(
    structureObjectIds: Array<ObjectId32>,
  ): Promise<Array<StructureLink> | Array<StructureLinkTemplate>> {
    if (structureObjectIds.length <= 0) {
      return [];
    }

    const smiO = getSmiByReferenceIdString(structureObjectIds[0]!);
    if (!smiO) throw new Error('No matching schema found, check structureObjectIds prefix');
    let smiL: SchemaMapItemType<ZodTypeAny, string>;
    if (smiO.dbId === SchemaMap.WorkData_StructureObjects.dbId) {
      smiL = SchemaMap.WorkData_StructureLinks;
    } else {
      smiL = SchemaMap.ResourceObjects_StructureLinkTemplates;
    }
    // this class may be created with a different smi than needed here
    const myOps = new baseROperations(smiL);
    const linkFilter: Filter<{ _id: ObjectId32 }> = {
      structureReferenceId: { $in: structureObjectIds },
    };
    const result = await myOps.getMany(linkFilter);

    // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- Safe: validated by Zod, type is context-dependent
    const typedResult = result.map(doc => smiL.schema.parse(doc)) as unknown as
      | Array<StructureLink>
      | Array<StructureLinkTemplate>;

    return typedResult;
  }

  private static async getAssemblyDocuments(
    structureObjectIds: Array<ObjectId32>,
  ): Promise<Array<AssemblyDocument> | Array<AssemblyDocumentTemplate>> {
    if (structureObjectIds.length <= 0) {
      return [];
    }
    const smiO = getSmiByReferenceIdString(structureObjectIds[0]!);
    if (!smiO) throw new Error('No matching schema found, check structureObjectIds prefix');
    let smiA: SchemaMapItemType<ZodTypeAny, string>;
    if (smiO.dbId === SchemaMap.WorkData_StructureObjects.dbId) {
      smiA = SchemaMap.WorkData_AssemblyDocuments;
    } else {
      smiA = SchemaMap.ResourceObjects_AssemblyDocumentTemplates;
    }
    // this class may be created with a different smi than needed here
    const myOps = new baseROperations(smiA);
    const linkFilter: Filter<{ _id: ObjectId32 }> = {
      structureReferenceId: { $in: structureObjectIds },
    };
    const result = await myOps.getMany(linkFilter);

    // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- Safe: validated by Zod, type is context-dependent
    const typedResult = result.map(doc => smiA.schema.parse(doc)) as unknown as
      | Array<AssemblyDocument>
      | Array<AssemblyDocumentTemplate>;

    return typedResult;
  }

  private static async getIamLinks(structureObjectIds: Array<ObjectId32>): Promise<Array<iamLink>> {
    if (structureObjectIds.length <= 0) {
      return [];
    }
    const smi = SchemaMap.IAM_AccessLinks;
    // this class may be created with a different smi than needed here
    const myOps = new baseROperations(smi);
    const iamFilter: Filter<{ _id: ObjectId32 }> = {
      $or: [
        { connectFromField: { $in: structureObjectIds } },
        { connectToField: { $in: structureObjectIds } },
      ],
    };
    const result = await myOps.getMany(iamFilter);

    const typedResult = result.map(doc => smi.schema.parse(doc));

    return typedResult;
  }
}
