// Identity Access Management

import { iamLinkSchema, iamNodeSchema } from '@tnt/zod-database-schemas';
import { SchemaMap } from '@tnt/zod-database-schemas';
import { AUTH_LEVEL } from '@tnt/shared-enums';
import { z } from 'zod/v4';

import { baseCRUDOperations } from '../baseOperations/baseCRUDOperations';

import type { AggregationCursor } from 'mongodb';
import type { iamLink, iamNode, ObjectId32 } from '@tnt/zod-database-schemas';

export class iamOperations<T extends { _id: ObjectId32 }> extends baseCRUDOperations<T> {
  // because the methods added are specific to this database, we need to fix
  // the SchemaMap to the proper one during the instantiation process
  constructor() {
    super(SchemaMap.IAM_AccessLinks);
  }

  // Example override method
  // public override async insert(
  //   data: WithOptionalObjectId32<T> | WithOptionalObjectId32<T>[],
  // ): Promise<T | Array<T> | z.ZodError<any>> {
  //   return [];
  // }

  public async getConnectedLinksFromDb(
    subjectId: ObjectId32,
    objectId: ObjectId32,
    authRequested: Array<z.infer<typeof AUTH_LEVEL>> | null,
  ): Promise<Array<iamLink>> {
    const col = await this.getCollection(this._schemaMapItem);

    if (authRequested === null) {
      // this will match any enum value to return entire map
      authRequested = Object.values(AUTH_LEVEL.Values);
    }

    interface AccessPathWrapper {
      accessPath: iamLink[];
    }

    //https://thecodebarbarian.com/a-nodejs-perspective-on-mongodb-34-graphlookup.html
    const retCursor: AggregationCursor<AccessPathWrapper> = col.aggregate<AccessPathWrapper>(
      //
      [
        {
          $match: { connectFromField: subjectId }, // Starting point
        },
        {
          $graphLookup: {
            from: 'AccessLinks',
            startWith: '$connectFromField',
            connectFromField: 'connectToField',
            connectToField: 'connectFromField',
            depthField: 'recursionDepth',
            as: 'accessPath',
            restrictSearchWithMatch: { authorizationLevel: { $in: authRequested } },
          },
        },
        {
          $project: {
            _id: 0,
            accessPath: 1,
          },
        },
        {
          $match: {
            'accessPath.connectToField': objectId,
          },
        },

        {
          $sort: {
            'accessPath.recursionDepth': 1,
          },
        },
      ],
      //
    );

    //https://www.mongodb.com/docs/drivers/node/current/fundamentals/crud/read-operations/cursor/
    // https://www.mongodb.com/community/forums/t/graphlookup-does-not-return-documents-in-order/10426/6

    const accessPath_sorted: AccessPathWrapper[] = [];
    if (retCursor !== null) {
      for await (const doc of retCursor) {
        accessPath_sorted.push(doc);
      }
    }
    //destructure the returned value -- similar to $unwind
    // is there an extra level of array here that needs to be unwound?
    const [a] = accessPath_sorted;
    const b = a?.accessPath ?? [];
    const parsed = z.array(iamLinkSchema).parse(b);
    return parsed;
  }

  public async getConnectedNodesFromDb(
    subjectId: ObjectId32,
    objectId: ObjectId32,
    authRequested: Array<z.infer<typeof AUTH_LEVEL>> | null,
  ): Promise<Array<iamNode>> {
    const col = await this.getCollection(this._schemaMapItem);

    if (authRequested === null) {
      // this will match any enum value to return entire map
      authRequested = Object.values(AUTH_LEVEL.Values);
    }

    interface AccessNodeWrapper {
      accessPath: iamNode[];
    }
    //https://thecodebarbarian.com/a-nodejs-perspective-on-mongodb-34-graphlookup.html
    const retCursor: AggregationCursor<AccessNodeWrapper> = col.aggregate<AccessNodeWrapper>([
      {
        $match: { connectFromField: subjectId }, // Starting point
      },
      {
        $graphLookup: {
          from: 'AccessLinks',
          startWith: '$connectFromField',
          connectFromField: 'connectToField',
          connectToField: 'connectFromField',
          depthField: 'recursionDepth',
          maxDepth: 20,
          as: 'accessPath',
          restrictSearchWithMatch: { authorizationLevel: { $in: authRequested } },
        },
      },
      {
        $project: {
          _id: 0,
          accessPath: 1,
        },
      },
      {
        $match: {
          'accessPath.connectToField': objectId, // Ensure path ends with Group_Drivers
        },
      },
      { $unwind: { path: '$accessPath' } },
      { $sort: { 'accessPath.recursionDepth': 1 } },
      // unable to get $sortArray to work?
      //  { $sortArray: { input: 'accessPath', sortBy: { recursionDepth: 1 } } },
      {
        $group: {
          _id: '$accessPath.connectFromField',
          links: {
            $addToSet: '$accessPath',
          },
        },
      },
    ]);

    //https://www.mongodb.com/docs/drivers/node/current/fundamentals/crud/read-operations/cursor/
    // https://www.mongodb.com/community/forums/t/graphlookup-does-not-return-documents-in-order/10426/6

    const accessPath_sorted: AccessNodeWrapper[] = [];

    if (retCursor !== null) {
      for await (const doc of retCursor) {
        accessPath_sorted.push(doc);
      }
    }
    const parsed = z.array(iamNodeSchema).parse(accessPath_sorted);
    return parsed;
  }

  public hasAccess(
    graph: Array<iamLink>,
    subjectId: string,
    objectId: string,
    authRequested: z.infer<typeof AUTH_LEVEL>,
  ): Array<iamLink> | string {
    const visited = new Set(); // Track visited nodes to prevent cycles

    // let pathNode: { node: string; path: Array<string> };
    const queue: Array<{ node: string; path: Array<string> }> = [{ node: subjectId, path: [] }]; // Queue of nodes to visit

    while (queue.length > 0) {
      const { node, path } = queue.shift() ?? { node: 'error!error!error!error!', path: [''] }; // Get the next node and its path

      if (visited.has(node)) continue; // Skip if already visited
      visited.add(node);

      // graph;
      // Find all links from the current node
      const links: Array<iamLink> = graph.filter(
        l => l.connectFromField.toString() === node && l.authorizationLevel === authRequested,
      );

      for (const link of links) {
        const newPath = [...path, link.connectFromField.toString()]; // Extend the path with the current link
        // console.log('newPath');
        // console.dirxml(inspect(newPath, { showHidden: true, colors: true, depth: null }));

        // Check if we've reached the target node
        if (link.connectToField.toString() === objectId) {
          const finalPath = [...newPath, link.connectToField.toString()];
          // console.log('AccessPath');
          // console.dirxml(finalPath);
          const completePath: Array<iamLink> | void = iamOperations.getNodePath(
            graph, // this needs to be an array of the links! not nodes.
            finalPath,
          );
          if (!completePath || completePath.length <= 0) return 'GRANTED: BUT PATH RECONSTRUCTION FAILED';
          return completePath;
        }

        // Add the next node to the queue for further exploration
        queue.push({ node: link.connectToField.toString(), path: newPath });
      }
    }

    return 'DENY: NO PATH FOUND'; // No valid path found
  }

  protected static getNodePath(links: Array<iamLink>, nodeMap: Array<string>): Array<iamLink> | void {
    if (links.length <= 0 || nodeMap.length <= 0) {
      console.error('Error: iamOperations.getNodePath: input arrays.length = 0');
      return;
    }
    const linkPath: Array<iamLink> = [];
    for (let index = 0; index < nodeMap.length - 1; index++) {
      const foundLink = links.find(
        link =>
          link.connectFromField.toString() === nodeMap[index] &&
          link.connectToField.toString() === nodeMap[index + 1],
      );
      if (!foundLink) {
        console.error('Error: iamOperations.getNodePath: Link not found');
        return;
      }
      linkPath.push(foundLink);
    }
    return linkPath;
  }
}
