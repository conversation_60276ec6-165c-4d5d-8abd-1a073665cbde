// import { z } from 'zod/v4';
// import { preprocessObjectId32 } from '../../common';
// import { InventoryDbSKUSchema } from '../commandSchemas';

// export const InventoryTransactionSkuCreateSchema = InventoryDbSKUSchema.extend({
//   transactionDataType: z.literal('Inventory.Skus.Create'),
//   productId: preprocessObjectId32,
// });
// export const InventoryTransactionSkuModifySchema = InventoryDbSKUSchema.extend({
//   transactionDataType: z.literal('Inventory.Skus.Modify'),
//   productId: preprocessObjectId32,
// });
// export const InventoryTransactionSkuSchema = InventoryDbSKUSchema.extend({
//   transactionDataType: z.enum(['Inventory.Skus.Create', 'Inventory.Skus.Modify']),
//   productId: preprocessObjectId32.optional(),
// });
// // this is used for typing transactions and extracting data of any type of sku transaction
// export type InventoryTransactionSku = z.infer<typeof InventoryTransactionSkuSchema>;
