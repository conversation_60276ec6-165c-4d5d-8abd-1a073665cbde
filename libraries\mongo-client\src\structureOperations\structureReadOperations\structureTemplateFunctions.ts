// functions to copy a structure from template db to a resource db
// and from a resource db to a template db
// updating all references, and including all needed documents
// TODO M-1:  incorporate AssemblyDocuments and IAMLinks?
// these functions should only create a structure in memory for use by the UI,
// it should not write to the databases as that should be a separate, distinct act

import { z } from 'zod/v4';
import { SchemaMap } from '@tnt/zod-database-schemas';
import { getObjectId32 } from '@tnt/zod-database-schemas/lib/common/preprocessObjectId32';

import { StructureReadOperations } from './structureReadOperations';

import type {
  AssemblyDocument,
  AssemblyDocumentTemplate,
  BaseStructureData,
  ObjectId32,
  SchemaMapItemType,
  StructureDataType,
  StructureLink,
  StructureLinkTemplate,
  StructureObject,
  StructureObjectTemplate,
} from '@tnt/zod-database-schemas';

// REVIEW should pulling the data from the database be left to other code? that way these functions may be used
// on data from UI that has not yet been saved?

// separate functionality
// getStructureFromDatabase should retrieve structure data from database (workData or Template) as BaseStructureData
// createNewStructure should take an output type (workData or Template) and BaseStructureData
// and return a new cloned/modified/copy that will reflect the desired output type?
// use lodash _.cloneDeep()

export async function workDataToTemplateExample(
  rootStructureObject: StructureObject,
): Promise<StructureDataType> {
  const myWdStructureOps = new StructureReadOperations(SchemaMap.ResourceObjects_StructureObjectTemplates);
  const structure = await myWdStructureOps.getStructuralContents(rootStructureObject);
  if (structure.objects === undefined || structure.objects.length < 1) throw new Error('no objects in array');

  const returnStructure = cloneRemapStructure(rootStructureObject, structure, 'template');
  return returnStructure;
}
/**
 *  clones and remaps a structure to a new output type
 * @param rootObj the root structure object, must be in structure.objects
 * @param structure the structure to clone
 * @param desiredOutputType either 'workData' or 'template'
 * @returns
 */
function cloneRemapStructure<
  TObject extends StructureObject | StructureObjectTemplate,
  TLink extends StructureLink | StructureLinkTemplate,
  TAssemblyDoc extends AssemblyDocument | AssemblyDocumentTemplate,
>(
  rootObj: TObject, // do not modify
  structure: BaseStructureData<TObject, TLink, TAssemblyDoc>, // clone
  desiredOutputType: 'workData' | 'template',
): StructureDataType {
  // new SchemaMapItemTypes
  let newSmiO: SchemaMapItemType<z.ZodTypeAny, string>;
  let newSmiL: SchemaMapItemType<z.ZodTypeAny, string>;
  let newSmiA: SchemaMapItemType<z.ZodTypeAny, string>;
  const newSmiI: SchemaMapItemType<z.ZodTypeAny, string> = SchemaMap.IAM_AccessLinks;

  if (desiredOutputType === 'template') {
    newSmiO = SchemaMap.ResourceObjects_StructureObjectTemplates;
    newSmiL = SchemaMap.ResourceObjects_StructureLinkTemplates;
    newSmiA = SchemaMap.ResourceObjects_AssemblyDocumentTemplates;
    // newSmiI = SchemaMap.IAM_AccessLinks;
  } else if (desiredOutputType === 'workData') {
    newSmiO = SchemaMap.WorkData_StructureObjects;
    newSmiL = SchemaMap.WorkData_StructureLinks;
    newSmiA = SchemaMap.WorkData_AssemblyDocuments;
    // newSmiI = SchemaMap.IAM_AccessLinks;
  } else {
    throw new Error('StructureTemplateFunctions: Error desiredOutputType not found');
  }

  // verify rootObj is in structure.objects -- should use id comparison or library deep-equal?
  // Method 1: Check by reference (if obj1 is the exact same object instance)
  const isContainedByReference = structure.objects.includes(rootObj);
  // Method 2: Check by _id (probably most common for objects with IDs)
  const isContainedById = structure.objects.some(item => item._id === rootObj._id);
  if (!isContainedById || !isContainedByReference) {
    throw new Error('StructureTemplateFunctions: rootObj not found in structure.objects');
  }

  // BRYAN I could not get this to work without directly referencing Schemas or using any[] and finally gave up???
  // Create a clone of the structure object to modify/use, will also want to transform the object to the new schema type
  // Zod .parse  -- From zod documentation: The value returned by .parse is a deep clone of the variable you passed in.
  const outStructure: StructureDataType = {
    objects: z.array(newSmiO.schema).parse(structure.objects),
    links: z.array(newSmiL.schema).parse(structure.links),
    assemblyDocuments: z.array(newSmiA.schema).parse(structure.assemblyDocuments),
    iamLinks: z.array(SchemaMap.IAM_AccessLinks.schema).parse(structure.iamLinks),
  };

  // create mapping table for new Ids as Map< oldId, newId >
  const idMap = new Map<string, ObjectId32>();
  outStructure.objects.forEach(obj => {
    // create ids for new database.collection
    const newId = getObjectId32(newSmiO.dbId + newSmiO.collectionId);
    idMap.set(obj._id, newId);
  });

  // modify outStructure objects
  for (const obj of outStructure.objects) {
    // Check for null or undefined on _id and structureRootReferenceId before accessing
    const newRootReferenceId = idMap.get(rootObj._id);
    if (!newRootReferenceId) {
      throw new Error('StructureTemplateFunctions: Root structure reference ID is missing in the mapping.');
    }

    obj._id = idMap.get(obj._id)!;
    obj.structureRootReference.containedById = newRootReferenceId;
    obj.structureReference.containedById = idMap.get(obj.structureReference.containedById)!;
  }

  // modify outStructure links
  for (const link of outStructure.links) {
    // create a new link._id
    link._id = getObjectId32(newSmiL.dbId + newSmiL.collectionId);

    // remap using idMap
    const mappedRootRefId = idMap.get(link.structureRootReference.containedById);
    if (!mappedRootRefId) {
      throw new Error('StructureTemplateFunctions: Link structure reference ID is missing in the mapping.');
    }
    link.structureRootReference.containedById = mappedRootRefId;
  }
  // modify outStructure assembly documents
  for (const doc of outStructure.assemblyDocuments) {
    // create a new doc._id
    doc._id = getObjectId32(newSmiA.dbId + newSmiA.collectionId);
    // Leave nulls as-is; otherwise remap using idMap
    doc.structureRootReferenceId =
      doc.structureRootReferenceId !== null
        ? (idMap.get(doc.structureRootReferenceId) ?? doc.structureRootReferenceId)
        : null;
    // Leave nulls as-is; otherwise remap using idMap
    doc.structureReferenceId =
      doc.structureReferenceId !== null
        ? (idMap.get(doc.structureReferenceId) ?? doc.structureReferenceId)
        : null;
  }

  // modify outStructure iamLinks
  for (const iamlink of outStructure.iamLinks) {
    // create a new iamlink._id
    iamlink._id = getObjectId32(newSmiI.dbId + newSmiI.collectionId);
    // Leave nulls as-is; otherwise remap using idMap
    iamlink.connectFromField = idMap.get(iamlink.connectFromField) ?? iamlink.connectFromField;
    iamlink.connectToField = idMap.get(iamlink.connectToField) ?? iamlink.connectToField;
  }

  return outStructure;
}
