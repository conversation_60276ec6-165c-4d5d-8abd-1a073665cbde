import { z } from 'zod/v4';

//https://github.com/colinhacks/zod/discussions/879
// since i want the ability to create a new date of now if blank, I can use default or preprocess.
// NOTE although new Date() creates a datetime in local timezone,
// when written to db, it is in utc!

// Define a custom type that allows both Date and string
export type DateOrString = Date | string | null;

// string count should be 0, 10, 19, 23, 29, consider writing an error to console if count is wrong?
export const preprocessDate = z.preprocess(input => {
  //   // Check if input is a date object
  if (Object.prototype.toString.call(input) === '[object String]') {
    // Check if input is null or undefined (optional?)
    if (input === null) {
      return input;
    }
    if ((input as string).length === 0) {
      return z.coerce.date().parse(new Date().toString());
    }
  }
  const result = z.coerce.date().safeParse(input);
  if (result.success) {
    return result.data;
  }
  throw result.error;
}, z.date()) as z.ZodType<DateOrString>;
