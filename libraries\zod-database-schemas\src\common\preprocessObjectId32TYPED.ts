import { z } from 'zod/v4';
import { generateObjectId } from '@tnt/shared-utilities';

import { SchemaMap } from '../schemaMapping';

import type { SchemaMapItemType } from '../schemaMapping';

// https://www.mongodb.com/docs/manual/core/document/#the-_id-field
// https://www.mongodb.com/docs/manual/reference/method/UUID/
// https://www.mongodb.com/docs/manual/reference/bson-types/
// https://www.geeksforgeeks.org/how-to-converting-objectid-to-string-in-mongodb/

// https://www.mongodb.com/blog/post/performance-best-practices-indexing

// Index keys that are of the BinData type are more efficiently stored in the index if:
// the binary subtype value is in the range of 0-7 or 128-135, and
// the length of the byte array is: 0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 20, 24, or 32.
// ------------ i believe that this is only true for UUID types? -------------------------------

// check input length to determine what type it is
// a length of 32 means it should be considered a valid TNT ObjectId
// a length of 8 means it should be considered a prefix for generating a valid TNT ObjectId
// any other length means it should be considered invalid
// a length of 24 means it should be considered a standard MongoDb ObjectId (invalid)

export function preprocessObjectId32TYPED(
  validSchemaMapItemTypes: SchemaMapItemType<z.ZodTypeAny, string>[],
): z.ZodTypeAny {
  const validPrefixes = getPrefixes(validSchemaMapItemTypes);
  return z.preprocess(
    (input: unknown) => {
      if (typeof input !== 'string') return input;

      // Expand valid 8-char prefix
      if (input.length === 8 && validPrefixes.includes(input)) {
        return input + generateObjectId();
      }

      // Leave other input as-is; Zod schema will validate or reject
      return input;
    },
    z
      .string()
      .length(32, { message: 'ObjectId32 must be exactly 32 characters long' })
      .refine(val => validPrefixes.some(prefix => val.startsWith(prefix)), {
        message: `ObjectId32 must start with a valid prefix: ${validPrefixes.join(', ')}`,
      })
      .refine(val => /^[a-zA-Z0-9]+$/.test(val), {
        message: 'ObjectId32 must contain only alphanumeric characters',
      }),
  );
}

function getPrefixes(smiArray: SchemaMapItemType<z.ZodTypeAny, string>[]): Array<string> {
  const retPrefixes: Set<string> = new Set();
  smiArray.forEach(smi => {
    retPrefixes.add(smi.dbId + smi.collectionId);
  });
  return [...retPrefixes];
}

// example usage
const _myschema = z.object({ itemRef: preprocessObjectId32TYPED([SchemaMap.ResourceObjects_ItemTypes]) });
