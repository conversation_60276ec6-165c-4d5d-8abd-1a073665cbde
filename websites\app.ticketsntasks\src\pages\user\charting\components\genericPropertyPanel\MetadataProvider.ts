import { FieldMetadata, FieldTypes } from './types';
import { JsonValue } from '../../../libraries/zod-database-schemas/src/common/functions/jsonAny';
import { z } from 'zod/v4';

/**
 * Creates a default metadata provider that infers field types from values
 * @param defaultMetadata Default metadata to apply to all fields
 * @returns A metadata provider function
 */
export function createDefaultMetadataProvider(defaultMetadata: Partial<FieldMetadata> = {}) {
  return (path: string, value: JsonValue): FieldMetadata => {
    const propertyName = path.split('.').pop() || path;

    // Infer field type based on value type
    let fieldType = FieldTypes.textBox;

    if (value === null || value === undefined) {
      fieldType = FieldTypes.textBox;
    } else if (typeof value === 'number') {
      fieldType = FieldTypes.number;
    } else if (typeof value === 'boolean') {
      fieldType = FieldTypes.checkbox;
    } else if (typeof value === 'object') {
      fieldType = Array.isArray(value) ? FieldTypes.array : FieldTypes.object;
    } else if (typeof value === 'string') {
      // Try to detect if string is a date
      if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
        fieldType = FieldTypes.datetime;
      } else if (/^\d{4}-\d{2}-\d{2}$/.test(value)) {
        fieldType = FieldTypes.date;
      } else if (/^#[0-9A-Fa-f]{6}$/.test(value)) {
        fieldType = FieldTypes.color;
      }
    }

    // Create label from property name (convert camelCase to Title Case)
    const label = propertyName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());

    return {
      type: fieldType,
      label,
      description: `${label} property`,
      hidden: false,
      readOnly: false,
      required: false,
      ...defaultMetadata,
    };
  };
}

/**
 * Creates a metadata provider that uses Zod schema metadata when available
 * @param schema Zod schema with metadata
 * @param fallbackProvider Fallback provider when Zod metadata is not available
 * @returns A metadata provider function
 */
export function createZodMetadataProvider(
  schema: z.ZodTypeAny,
  fallbackProvider: (path: string, value: JsonValue) => FieldMetadata,
) {
  return (path: string, value: JsonValue): FieldMetadata => {
    // TODO: Implement Zod schema metadata extraction
    // This would require knowledge of how your Zod schemas are structured

    // For now, just use the fallback provider
    return fallbackProvider(path, value);
  };
}
