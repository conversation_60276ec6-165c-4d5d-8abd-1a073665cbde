import { z } from 'zod/v4';

// zod3 implementation
// export const coerceBigInt = z.object({
//   id: z.any().transform((value, ctx) => {
//     try {
//       return BigInt(value as bigint | boolean | number | string);
//     } catch {
//       ctx.addIssue({
//         code: 'invalid_type',
//         expected: 'unknown',
//         received: value as
//           | 'string'
//           | 'number'
//           | 'bigint'
//           | 'boolean'
//           | 'symbol'
//           | 'undefined'
//           | 'object'
//           | 'function'
//           | 'map'
//           | 'nan'
//           | 'integer'
//           | 'float'
//           | 'date'
//           | 'null'
//           | 'array'
//           | 'unknown'
//           | 'promise'
//           | 'void'
//           | 'never'
//           | 'set',
//         message: `Can't be parsed to BigInt`,
//       });
//     }
//   }),
// });

// zod4 implementation
export const coerceBigInt = z.object({
  id: z.any().transform((value, ctx) => {
    try {
      return BigInt(value as string | number | boolean | bigint);
    } catch {
      ctx.addIssue({
        code: 'invalid_type',
        expected: 'bigint',
        received: typeof value as
          | 'string'
          | 'number'
          | 'bigint'
          | 'boolean'
          | 'symbol'
          | 'undefined'
          | 'object'
          | 'function',
        input: value,
        message: `Can't be parsed to BigInt`,
      });
      return z.NEVER;
    }
  }),
});
