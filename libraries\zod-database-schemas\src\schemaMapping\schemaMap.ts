import { ThemeSchema } from '../application';
import {
  AddressSchema,
  ApiCallSchema,
  ApiHostConnectionSchema,
  AssemblyDocumentTemplateSchema,
  ContactSchema,
  DocumentSchema,
  EntitySchema,
  EquipmentSchema,
  GroupSchema,
  iamLinkSchema,
  ItemTypeSchema,
  RaciTrackingSchema,
} from '../resources';
import { WorkEntrySchema } from '../workData/workEntry/workEntry';
import { ResourceRequestSchema } from '../resources/resourceObjectRequest';
import { AssemblyDocumentSchema, ResourceEntrySchema } from '../workData';
import { StructureLinkTemplateSchema } from '../resources/workDataTemplates/structureTemplates/structureLinkTemplate';
import { StructureLinkSchema } from '../workData/structures/structureLink';
import { StructureObjectSchema } from '../workData/structures/structureObject';
import { StructureObjectTemplateSchema } from '../resources/workDataTemplates/structureTemplates/structureObjectTemplate';
import {
  InventoryDbItemSchema,
  InventoryDbLocationSchema,
  InventoryDbProductSchema,
  InventoryDbSKUSchema,
  InventoryDbStockLocationSchema,
  InventoryTransactionSchema,
} from '../inventory';
import { StructureSchemeItemSchema } from '../resources/workDataTemplates/structureTemplates';

import type { ZodTypeAny } from 'zod/v4';
import type { IamLinkSchema } from '../accessControl/iamLink';
import type { SchemaMapItemType } from './types';

// Define index types
// type IndexKey = {
//   [key: string]: number | string;
// };

// type IndexOptions = {
//   unique?: boolean;
//   name?: string;
//   // Add other MongoDB index options as needed
// };

// type IndexDefinition = {
//   key: IndexKey;
//   options?: IndexOptions;
// };

// type SchemaMapItemType<T extends z.ZodTypeAny, R extends string> = {
//   referenceString: string;
//   referenceId: R;
//   dbName: string;
//   dbId: string;
//   collectionName: string;
//   collectionId: string;
//   schema: T;
//   indexes: IndexDefinition[] | [];
//   serverUri: string;
// };

// type SchemaRegistryType = {
//     database1Collection1Key: SchemaEntryType<typeof myObjectSchema1, 'DB1_COL1'>;
//     database1Collection2Key: SchemaEntryType<typeof myObjectSchema2, 'DB1_COL2'>;
//   };

type SchemaMapType = {
  // ---------------   Access Control -----------------
  IAM_AccessLinks: SchemaMapItemType<typeof IamLinkSchema, 'IAM_ALIN'>;
  // ---------------   APPSETTINGS   -----------------
  Application_Theme: SchemaMapItemType<typeof ThemeSchema, 'APPSTHEM'>;
  // ---------------   INVENTORY   -----------------
  Inventory_Products: SchemaMapItemType<typeof InventoryDbProductSchema, 'INV_PROD'>;
  Inventory_Transactions: SchemaMapItemType<typeof InventoryTransactionSchema, 'INV_TRAN'>;
  Inventory_Locations: SchemaMapItemType<typeof InventoryDbLocationSchema, 'INV_LOCA'>;
  Inventory_StockLocations: SchemaMapItemType<typeof InventoryDbStockLocationSchema, 'INV_SLOC'>;
  Inventory_Skus: SchemaMapItemType<typeof InventoryDbSKUSchema, 'INV_SKUS'>;
  Inventory_Items: SchemaMapItemType<typeof InventoryDbItemSchema, 'INV_ITEM'>;
  // ---------------   RESOURCEOBJECTS -----------------
  ResourceObjects_Addresses: SchemaMapItemType<typeof AddressSchema, 'ROBJADDR'>;
  ResourceObjects_ApiCalls: SchemaMapItemType<typeof ApiCallSchema, 'ROBJAPIC'>;
  ResourceObjects_ApiHostConnections: SchemaMapItemType<typeof ApiHostConnectionSchema, 'ROBJAPIH'>;
  ResourceObjects_AssemblyDocumentTemplates: SchemaMapItemType<
    typeof AssemblyDocumentTemplateSchema,
    'ROBJASMD'
  >;
  ResourceObjects_Contacts: SchemaMapItemType<typeof ContactSchema, 'ROBJCONT'>;
  ResourceObjects_Documents: SchemaMapItemType<typeof DocumentSchema, 'ROBJDOCU'>;
  ResourceObjects_Entities: SchemaMapItemType<typeof EntitySchema, 'ROBJENTI'>;
  ResourceObjects_Equipment: SchemaMapItemType<typeof EquipmentSchema, 'ROBJEQUI'>;
  ResourceObjects_Groups: SchemaMapItemType<typeof GroupSchema, 'ROBJGROU'>;
  ResourceObjects_ItemTypes: SchemaMapItemType<typeof ItemTypeSchema, 'ROBJITYP'>;
  ResourceObjects_StructureLinkTemplates: SchemaMapItemType<typeof StructureLinkTemplateSchema, 'ROBJSLNT'>;
  ResourceObjects_StructureObjectTemplates: SchemaMapItemType<
    typeof StructureObjectTemplateSchema,
    'ROBJSOBT'
  >;
  ResourceObjects_StructureSchemeItems: SchemaMapItemType<typeof StructureSchemeItemSchema, 'ROBJSSCH'>;
  ResourceObjects_Racis: SchemaMapItemType<typeof RaciTrackingSchema, 'ROBJRACI'>;
  ResourceObjects_Requests: SchemaMapItemType<typeof ResourceRequestSchema, 'ROBJREQU'>;
  // ---------------   WORKDATA  -----------------
  WorkData_AssemblyDocuments: SchemaMapItemType<typeof AssemblyDocumentSchema, 'WDATASMD'>;
  //  //The inferred type of this node exceeds the maximum length the compiler will serialize. An explicit type annotation is needed.
  WorkData_ResourceEntries: SchemaMapItemType<typeof ResourceEntrySchema, 'WDATREEN'>;
  WorkData_StructureLinks: SchemaMapItemType<typeof StructureLinkSchema, 'WDATSLNK'>;
  WorkData_StructureObjects: SchemaMapItemType<typeof StructureObjectSchema, 'WDATSOBJ'>; // The inferred type of this node exceeds the maximum length the compiler will serialize. An explicit type annotation is needed.
  WorkData_WorkEntries: SchemaMapItemType<typeof WorkEntrySchema, 'WDATWENT'>;
};

// Explicit Type Annotation - used to replace Typescript inline inference and resultant error ts7056
// The inferred type of this node exceeds the maximum length the compiler will serialize.
// Give the compiler the shape up front.
// It no longer tries to serialize the whole giant literal inline.
// No runtime impact, pure compile-time fix
// schema accessed by direct reference
// each entity accessed by Key and referenceId
// Intellisense and type safety work perfectly.

const SchemaMapEntries = {
  // ---------------   Access Control -----------------

  IAM_AccessLinks: {
    referenceString: 'IAM_AccessLinks',
    referenceId: 'IAM_ALIN',
    dbName: 'IAM',
    dbId: 'IAM_',
    collectionName: 'AccessLinks',
    collectionId: 'ALIN',
    schema: iamLinkSchema,
    indexes: [], // Type error here!
    serverUri: '',
    // dataGroupName: 'IAM',
  },

  // ---------------   APPSETTINGS   -----------------
  Application_Theme: {
    referenceString: 'Application_Theme',
    referenceId: 'APPSTHEM',
    dbName: 'Application',
    dbId: 'APPS',
    collectionName: 'Theme',
    collectionId: 'THEM',
    schema: ThemeSchema,
    indexes: [
      { key: { _id: 1 }, options: { unique: true } },
      { key: { themeName: 1 }, options: { unique: true } },
    ],
    // indexes: ['_id', 'themeName'] satisfies Array<keyof z.infer<typeof ThemeSchema>>, // does not work with nested array indexes
    serverUri: '',
    // dataGroupName: null,
  },

  // ---------------   INVENTORY   -----------------
  Inventory_Products: {
    referenceString: 'Inventory_Products',
    referenceId: 'INV_PROD',
    dbName: 'Inventory',
    dbId: 'INV_',
    collectionName: 'Products',
    collectionId: 'PROD',
    schema: InventoryDbProductSchema,
    indexes: [
      { key: { productName: 1 }, options: { unique: true, name: 'indexName' } },
      { key: { productCode: 1 }, options: { unique: true } },
      { key: { 'product.skus._id': 1 }, options: { unique: true } },
      { key: { 'product.skus.skuCode': 1 }, options: { unique: true } },
      { key: { 'product.skus.stockLocations._id': 1 }, options: { unique: true } },
      { key: { 'product.skus.stockLocations.locationId': 1 }, options: { unique: true } },
    ],
    serverUri: '',
    // dataGroupName: null,
  },
  Inventory_Transactions: {
    referenceString: 'Inventory_Transactions',
    referenceId: 'INV_TRAN',
    dbName: 'Inventory',
    dbId: 'INV_',
    collectionName: 'Transactions',
    collectionId: 'TRAN',
    schema: InventoryTransactionSchema,
    indexes: [
      { key: { referenceId: 1 }, options: { unique: true } },
      { key: { transactionReference: 1 }, options: { unique: true } },
    ],
    serverUri: '',
    // dataGroupName: null,
  },
  Inventory_Locations: {
    referenceString: 'Inventory_Locations',
    referenceId: 'INV_LOCA',
    dbName: 'Inventory',
    dbId: 'INV_',
    collectionName: 'Locations',
    collectionId: 'LOCA',
    schema: InventoryDbLocationSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },
  Inventory_StockLocations: {
    referenceString: 'Inventory_StockLocations',
    referenceId: 'INV_SLOC',
    dbName: '',
    dbId: '',
    collectionName: '',
    collectionId: '',
    schema: InventoryDbStockLocationSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },
  Inventory_Skus: {
    referenceString: 'Inventory_Skus',
    referenceId: 'INV_SKUS',
    dbName: '',
    dbId: '',
    collectionName: '',
    collectionId: '',
    schema: InventoryDbSKUSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },
  Inventory_Items: {
    referenceString: 'Inventory_Items',
    referenceId: 'INV_ITEM',
    dbName: '',
    dbId: '',
    collectionName: '',
    collectionId: '',
    schema: InventoryDbItemSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  // ---------------   RESOURCEOBJECTS -----------------
  ResourceObjects_Addresses: {
    referenceString: 'ResourceObjects_Addresses',
    referenceId: 'ROBJADDR',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'Addresses',
    collectionId: 'ADDR',
    schema: AddressSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  ResourceObjects_ApiCalls: {
    referenceString: 'ResourceObjects_ApiCalls',
    referenceId: 'ROBJAPIC',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'ApiCalls',
    collectionId: 'APIC',
    schema: ApiCallSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },
  ResourceObjects_ApiHostConnections: {
    referenceString: 'ResourceObjects_ApiHostConnections',
    referenceId: 'ROBJAPIH',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'ApiHostConnections',
    collectionId: 'APIH',
    schema: ApiHostConnectionSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  ResourceObjects_AssemblyDocumentTemplates: {
    referenceString: 'ResourceObjects_AssemblyDocumentTemplates',
    referenceId: 'ROBJASMD',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'AssemblyDocumentTemplates',
    collectionId: 'ASMD',
    schema: AssemblyDocumentTemplateSchema,
    serverUri: '',
    // dataGroupName: 'WDATDataGroup',
    indexes: [],
  },

  ResourceObjects_Contacts: {
    referenceString: 'ResourceObjects_Contacts',
    referenceId: 'ROBJCONT',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'Contacts',
    collectionId: 'CONT',
    schema: ContactSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  ResourceObjects_Documents: {
    referenceString: 'ResourceObjects_Documents',
    referenceId: 'ROBJDOCU',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'Documents',
    collectionId: 'DOCU',
    schema: DocumentSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },
  ResourceObjects_Entities: {
    referenceString: 'ResourceObjects_Entities',
    referenceId: 'ROBJENTI',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'Entities',
    collectionId: 'ENTI',
    schema: EntitySchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },
  ResourceObjects_Equipment: {
    referenceString: 'ResourceObjects_Equipment',
    referenceId: 'ROBJEQUI',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'Equipment',
    collectionId: 'EQUI',
    schema: EquipmentSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },
  ResourceObjects_Groups: {
    referenceString: 'ResourceObjects_Groups',
    referenceId: 'ROBJGROU',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'Groups',
    collectionId: 'GROU',
    schema: GroupSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  ResourceObjects_ItemTypes: {
    referenceString: 'ResourceObjects_ItemTypes',
    referenceId: 'ROBJITYP',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'ItemTypes',
    collectionId: 'ITYP',
    schema: ItemTypeSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  ResourceObjects_StructureLinkTemplates: {
    referenceString: 'ResourceObjects_StructureLinkTemplates',
    referenceId: 'ROBJSLNT',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'StructureLinkTemplates',
    collectionId: 'SLNT',
    schema: StructureLinkTemplateSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  ResourceObjects_StructureObjectTemplates: {
    referenceString: 'ResourceObjects_StructureObjectTemplates',
    referenceId: 'ROBJSOBT',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'StructureObjectTemplates',
    collectionId: 'SOBT',
    schema: StructureObjectTemplateSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  ResourceObjects_StructureSchemeItems: {
    referenceString: 'ResourceObjects_StructureSchemes',
    referenceId: 'ROBJSSCH',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'StructureSchemeItems',
    collectionId: 'SSCH',
    schema: StructureSchemeItemSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  ResourceObjects_Racis: {
    referenceString: 'ResourceObjects_Racis',
    referenceId: 'ROBJRACI',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'Raci',
    collectionId: 'RACI',
    schema: RaciTrackingSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },
  ResourceObjects_Requests: {
    referenceString: 'ResourceObjects_Requests',
    referenceId: 'ROBJREQU',
    dbName: 'ResourceObjects',
    dbId: 'ROBJ',
    collectionName: 'Requests',
    collectionId: 'REQU',
    schema: ResourceRequestSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  // ---------------   WORKDATA  -----------------

  WorkData_AssemblyDocuments: {
    referenceString: 'WorkData_AssemblyDocuments',
    referenceId: 'WDATASMD',
    dbName: 'WorkData',
    dbId: 'WDAT',
    collectionName: 'AssemblyDocuments',
    collectionId: 'ASMD',
    schema: AssemblyDocumentSchema,
    serverUri: '',
    // dataGroupName: 'WDATDataGroup',
    indexes: [],
  },

  WorkData_ResourceEntries: {
    referenceString: 'WorkData_ResourceEntries',
    referenceId: 'WDATREEN',
    dbName: 'WorkData',
    dbId: 'WDAT',
    collectionName: 'ResourceEntries',
    collectionId: 'REEN',
    schema: ResourceEntrySchema,
    serverUri: '',
    // dataGroupName: 'WDATDataGroup',
    indexes: [],
  },

  WorkData_StructureLinks: {
    referenceString: 'WorkData_StructureLinks',
    referenceId: 'WDATSLNK',
    dbName: 'WorkData',
    dbId: 'WDAT',
    collectionName: 'StructureLinks',
    collectionId: 'SLNK',
    schema: StructureLinkSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  WorkData_StructureObjects: {
    referenceString: 'WorkData_StructureObjects',
    referenceId: 'WDATSOBJ',
    dbName: 'WorkData',
    dbId: 'WDAT',
    collectionName: 'StructureObjects',
    collectionId: 'SOBJ',
    schema: StructureObjectSchema,
    serverUri: '',
    // dataGroupName: null,
    indexes: [],
  },

  WorkData_WorkEntries: {
    referenceString: 'WorkData_WorkEntries',
    referenceId: 'WDATWENT',
    dbName: 'WorkData',
    dbId: 'WDAT',
    collectionName: 'WorkEntries',
    collectionId: 'WENT',
    schema: WorkEntrySchema,
    serverUri: '',
    // dataGroupName: 'WDATDataGroup',
    indexes: [],
  },
} as const satisfies SchemaMapType;

//
export const SchemaMap: SchemaMapType = SchemaMapEntries;

// probaby not needed
// type InferredShape<K extends keyof SchemaRegistryType> = z.infer<SchemaRegistryType[K]['schema']>;

// adding access by referenceString
type SchemaMapKeys = keyof SchemaMapType;
export function getSmiByKey<K extends SchemaMapKeys>(key: K): SchemaMapType[K] {
  return SchemaMap[key];
}

// adding access by key string
// using a string
/**
 *
 * @param refId - string to match to referenceString
 * @returns SchemaMapItemType
 */
export function getSmiByKeyString(refId: string): SchemaMapItemType<ZodTypeAny, string> | undefined {
  for (const key in SchemaMap) {
    if (Object.prototype.hasOwnProperty.call(SchemaMap, key)) {
      const entry = SchemaMap[key as keyof SchemaMapType];
      if (entry.referenceString === refId) {
        return entry;
      }
    }
  }
  return undefined; // If no match found
}

// adding access by referenceId
// using a string
/**
 *
 * @param refId - string, either ObjectId32 with prefix, or the prefix alone representing the referenceId
 * @returns SchemaMapItemType
 */
export function getSmiByReferenceIdString(refId: string): SchemaMapItemType<ZodTypeAny, string> | undefined {
  for (const key in SchemaMap) {
    if (Object.prototype.hasOwnProperty.call(SchemaMap, key)) {
      const entry = SchemaMap[key as keyof SchemaMapType];
      if (entry.referenceId === refId.substring(0, 8)) {
        return entry;
      }
    }
  }
  return undefined; // If no match found
}

// using a enumerated value (intellisense support for selection)
type ReferenceIdToKey = {
  [K in keyof SchemaMapType as SchemaMapType[K]['referenceId']]: K;
};
export function getSmiByReferenceId<R extends keyof ReferenceIdToKey>(
  refId: R,
): SchemaMapType[ReferenceIdToKey[R]] {
  for (const key in SchemaMap) {
    if (Object.prototype.hasOwnProperty.call(SchemaMap, key)) {
      const entry = SchemaMap[key as keyof SchemaMapType];
      if (entry.referenceId === refId) {
        return entry as SchemaMapType[ReferenceIdToKey[R]];
      }
    }
  }
  throw new Error(`Schema with referenceId "${refId}" not found`);
}

// adding indexing functionality
type SchemaMapEntryUnion = SchemaMapType[keyof SchemaMapType];
export function getAllSchemaEntries(): SchemaMapEntryUnion[] {
  return Object.values(SchemaMap);
}

// // Example usage direct reference:
// SchemaMap.Application_Theme.schema.parse('');

// // Example usage by Key:
// // intellisense does work with the key entry here
// const appThemeSmi1 = getSmiByKey('Application_Theme');
// appThemeSmi1.schema.parse('');
// if (appThemeSmi1) {
//   const result = appThemeSmi1.schema.parse({ name: 'Alice', age: 30 });
//   console.log('Parsed:', result);
// }

// // Example usage by referenceId:
// // intellisense does work with the referenceId here
// const appThemeSmi2 = getSmiByReferenceId('APPSTHEM'); // could also use 'APPSTHEM349923ik3a89f3od969ac852a'
// if (appThemeSmi1) {
//   const result = appThemeSmi1.schema.parse({ name: 'Alice', age: 30 });
//   console.log('Parsed:', result);
// }

// // Usage example with random string
// const randomString = 'DB1_COL1'; // This could come from anywhere, for example, user input
// const smi2 = getSmiByReferenceIdString(randomString);
// if (smi2) {
//   const result = appThemeSmi1.schema.parse({ name: 'Alice', age: 30 });
//   console.log('Parsed:', result);
// }

// // example indexing through
// const schemas = getAllSchemaEntries();
// schemas.forEach(schema => {
//   console.log(`referenceString: ${schema.referenceString}, referenceId: ${schema.referenceId}`);
// });
