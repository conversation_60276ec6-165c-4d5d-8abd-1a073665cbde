// use entity from resources for warehouses, branches, ...

// should this be named unit rather than item?

import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId, preprocessObjectId32 } from '../../common';

export const InventoryDbItemSchema = z.object({
  _id: preprocessObjectId, // since this is NOT a base collection document, it does NOT use a preprocessObjectID32!
  productId: preprocessObjectId32, // product, sku, and location id are included to simplify passing of parameters, may become optional
  skuId: preprocessObjectId,
  assignedStructureObjects: z
    .array(z.object({ structureObject: preprocessObjectId32, quantity: z.number() }))
    .optional()
    .nullable(),
  id1: z.string(), // default here should be skuCode if itemIds are not used?
  id1Name: preprocessObjectId32.optional().nullable().default(null), // ROBJITYP should be created as lot, batch, serial
  id2: z.string().optional().nullable().default(null),
  id2Name: preprocessObjectId32.optional().nullable().default(null), // ROBJITYP should be user created as lot, batch, serial
  id3: z.string().optional().nullable().default(null),
  id3Name: preprocessObjectId32.optional().nullable().default(null), // ROBJITYP should be user created as lot, batch, serial
  itemSum: z.number().default(0),
  itemReservedSum: z.number().default(0),
  units: preprocessObjectId32, // ROBJITYP preprocessObjectId32
  qualityControlStatus: preprocessObjectId32.optional(), // ROBJITYP preprocessObjectId32
  // manufacturerId: preprocessObjectId32.optional().nullable().default(null), // EntitySchema.optional(),
  // manufacturerProductId: z.string(),
  supplierId: preprocessObjectId32.optional().nullable().default(null), // EntitySchema.optional(),
  supplierLead: z
    .object({ value: z.number().int(), units: preprocessObjectId32 })
    .optional()
    .nullable()
    .default(null),
  manufacturedDate: preprocessDate.optional().nullable().default(null),
  purchasedDate: preprocessDate.optional().nullable().default(null),
  expirationDate: preprocessDate.optional().nullable().default(null),
  referenceDocs: z
    .array(z.object({ docType: preprocessObjectId32, docId: preprocessObjectId32 }))
    .optional()
    .nullable(), // this would reference drawing, images, in the format {ROBJITYP, ROBJDOCU}...

  // REVIEW how to allow builder to create their own identifiers?
  // BRYAN is this where your usage of record and map would work?
  // see inventoryOperations/productTreePipeline.ts
  // const addFilteredItems = (items: Array<Record<string, string>>, active?: boolean) => ({
  variantIdentifiers: z.array(preprocessObjectId32).optional().nullable().default(null), // should be an array of ROBJITYPs of GroupName "InventoryLocationDescriptor"
  additionalProperties: z.any().optional().nullable().default(null),
  tags: z.array(z.string().optional().nullable().default(null)).optional().nullable().default([]),

  // use passthrough or z.any() for other properties to allow user customization?
  //Key inventory fields for pharmaceutical industries include:
  // product code, product name, description, batch number, expiration date,
  // quantity on hand, location, supplier details, purchase date, cost per unit,
  // reorder point, storage conditions, controlled substance flag, lot number,
  // manufacturing date, quality control status, and regulatory compliance details;
  // all crucial for tracking and managing medications while adhering to strict industry regulations.
});

export type InventoryDbItem = z.infer<typeof InventoryDbItemSchema>;
