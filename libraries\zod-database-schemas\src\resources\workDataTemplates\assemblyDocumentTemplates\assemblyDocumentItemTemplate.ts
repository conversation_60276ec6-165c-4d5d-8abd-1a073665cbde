import { z } from 'zod/v4';

import { BomItemTemplateSchema } from './bomItemTemplate';
import { RafItemTemplateSchema } from './rafItemTemplate';
import { CheckSheetItemTemplateSchema } from './checkSheetItemTemplate';
import { CheckListItemTemplateSchema } from './checkListItemTemplate';

// create a compound index for name/version
// db.boms.createIndex(
//  { checkSheetName: 1, checkSheetVersion: 1 },
//   { unique: true }
// )

// used for BOMs/RAF/Checksheets -
// difference between?
// BOMs extract items from inventory to be used
// RAFs insert items into inventory
// checksheets are tally sheets and simply count items/procedures and document their status

// should BOMs support multiple levels? NO, a bom should be built to only apply to a single ticket,
//  which represents a sub-assembly in a project/process
// BOMs can then be aggregated in a task, job, project

export const AssemblyDocumentItemTemplateSchema = z.discriminatedUnion('assemblyDefType', [
  BomItemTemplateSchema,
  RafItemTemplateSchema,
  CheckSheetItemTemplateSchema,
  CheckListItemTemplateSchema,
]);

export type AssemblyDocumentItemTemplate = z.infer<typeof AssemblyDocumentItemTemplateSchema>;
