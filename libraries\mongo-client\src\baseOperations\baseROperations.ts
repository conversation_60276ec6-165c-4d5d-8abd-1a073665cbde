import { type Filter } from 'mongodb';
import { Logger } from '@tnt/error-logger';

import { baseDbOperations } from './baseDbOperations';

import type { ObjectId32, SchemaMapItemType } from '@tnt/zod-database-schemas';
import type { QueryOptions } from '../types';
import type { ZodTypeAny } from 'zod/v4';

// provides basic Read operations
export class baseROperations<T extends { _id: ObjectId32 }> extends baseDbOperations<T> {
  // TODO M-1:  -- should these parse after fetching and before returning?
  // if not, defaults may not be shown to consumer for fields not in db
  // this was getAll, but then why have filter? renamed and added filter in args
  /**
   *
   * @param options MongoDb Query Options
   * @param filter MongoDb Filter<T>
   * @returns Promise<Array<T>
   */

  logger = new Logger({ serviceName: '@tnt/mongo-client' });
  async getMany(
    filter?: Filter<T>,
    options?: QueryOptions,
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<Array<T>> {
    const { info } = options ?? {};
    const col = await this.getCollection(smi);

    try {
      const result = await col
        .find(filter ?? ({} as Filter<T>), {
          projection: info,
        })
        .toArray();
      return result as Array<T>;
    } catch (err: unknown) {
      this.logger.error(`baseROperations:getMany: error`, err);
      throw new Error(`Failed to find the document by provided id: ${JSON.stringify(filter)}`);
    }
  }

  // TODO M-1:  -- should these parse after fetching and before returning?
  // if not, defaults may not be shown to consumer for fields not in db
  /**
   *
   * @param id as ObjectId32 of object to retrieve
   * @param options QueryOptions
   * @returns Promise<T>
   */
  async getById(
    id: string,
    options?: QueryOptions,
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<T> {
    const { info } = options ?? {};
    const col = await this.getCollection(smi);

    const result = await col
      .findOne({ _id: { $eq: `${id}` } } as Filter<T>, {
        projection: info,
      })
      .catch((err: unknown) => {
        this.logger.error(`baseROperations:getById: error`, err);
        throw new Error(`Failed to find the document by provided id: ${id}`);
      });

    return result as T;
  }
}
