// eslint.config.ts for consuming projects

import nodeConfig from '@tnt/eslint-config/node';

import { dirname, resolve } from 'path';
import { fileURLToPath } from 'url';
const __dirname = dirname(fileURLToPath(import.meta.url));

export default [
  ...nodeConfig,
  {
    // Package-specific overrides with correct package path
    files: [
      resolve(__dirname, './src/**/*.ts'),
      resolve(__dirname, './src/**/*.tsx'),
      resolve(__dirname, './tests/**/*.ts'),
    ],
    languageOptions: {
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },
];
