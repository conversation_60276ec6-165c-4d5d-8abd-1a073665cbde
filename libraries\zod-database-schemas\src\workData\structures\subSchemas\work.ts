import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../../common';

import { StructureObjectBaseSchema } from './base';

// this type of structure object cannot be referenced in structureReference or structureRootReference
// this is the ONLY type of structure object that can be referenced by WorkEntry, AssemblyDocuments, ...
export const StructureObjectWorkSchema = StructureObjectBaseSchema.extend({
  structureObjectType: z.literal('structureObjectWork'),

  // Resources available
  // these should be made available through iamLinks.

  // work entry container data
  // access limited workGroup members create/add/edit work items such as checkListItemEntry, checkSheetsItemEntry, workEntry
  // This should be either a contact or a group reference,
  // TODO M-1:  this should be done with IAMLinks instead with a work entry group access?
  workGroup: z.array(preprocessObjectId32),

  // Work Data entries/status for checklists/checksheets/workentries/resourceEntries, ... are all contained in
  // their own databases and linked back to the container/project/user by these ids in their entry
  // structureRootReferenceId: preprocessObjectId32.optional().nullable(), // this should be the main process/project
  // structureReferenceId: preprocessObjectId32.optional().nullable(), // this is the specific structure container in that main process/project
  // resourceId: preprocessObjectId32,

  // assembly items such as bill of materials/report as finished/checkLists/checkSheets should be at work entry level
  // if materials are used, produced, referenced in a different work entry container, then they should be
  // created and used there. this will promote a larger breakdown of items correctly into subassemblies.
  // a single item of each type, bom/raf/cl/cs should be allowed for each ticket.
});

export type StructureObjectWork = z.infer<typeof StructureObjectWorkSchema>;
