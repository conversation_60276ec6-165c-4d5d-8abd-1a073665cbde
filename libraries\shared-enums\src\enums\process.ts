import { z } from 'zod/v4';

const PRECEDENCE_TYPE = z.enum(['FINISH_TO_START', 'START_TO_START', 'FINISH_TO_FINISH', 'START_TO_FINISH']);

const CONTAINER_TYPE = z.enum([
  'Lx', // wbObject Lx container (Project, Job, ...)
  'Ln-1', // wbObject  Ln-1 (Task) contains only Ln wbObjects and PATH
  'Ln', // wbObject  Ln (Ticket
  'DECISION',
  'START',
  'END',
  'IO',
  'PATH',
]);

const PROCESS_TERMINOLOGY_TYPE = z.enum([
  'PREDICTIVE', // project/job/task/ticket
  // TODO  verify how are these would align between the containers used in TNT
  // https://www.atlassian.com/agile/project-management/epics
  // https://www.geeksforgeeks.org/what-is-sprint-iteration-and-increment/
  'AGILE', // product/epic(increment)/sprint(iteration)/story
]);

const CONTENT_PATH_TYPES_ALLOWED = z.enum(['linear', 'loop', 'none', 'parallel']);

const EDGE_TYPE = z.enum(['oneWayArrow', 'twoWayArrow', 'transparent', 'dashed', 'thick']);

const OBJECT_PROCESS_STATUS = z.enum([
  'INITIATION',
  'PLANNING',
  'WAIT_FOR_START', //planning complete, project has started, but the ticket is not active yet
  'PRE_EXECUTION', // single pass execution of any required jobs such as API calls
  'PRE_EXECUTION_API_RESPONSE_WAIT',
  'EXECUTING',
  'POST_EXECUTION', // single pass execution of any required jobs such as API calls
  'POST_EXECUTION_API_RESPONSE_WAIT',
  'COMPLETE',
  'ABANDONED',
  'SKIPPED',
  'BYPASSED',
]);

export {
  CONTAINER_TYPE,
  CONTENT_PATH_TYPES_ALLOWED,
  EDGE_TYPE,
  OBJECT_PROCESS_STATUS,
  PRECEDENCE_TYPE,
  PROCESS_TERMINOLOGY_TYPE,
};
