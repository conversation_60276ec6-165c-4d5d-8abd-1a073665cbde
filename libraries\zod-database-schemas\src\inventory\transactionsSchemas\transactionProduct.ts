// import { z } from 'zod/v4';
// import { InventoryDbProductSchema } from '../commandSchemas/product';
// import { InventoryDbSKUSchema } from '../commandSchemas/sku';

// export const InventoryTransactionProductCreateSchema = InventoryDbProductSchema.extend({
//   transactionDataType: z.literal('Inventory.Products.Create'),
//   skus: z.array(InventoryDbSKUSchema).default([]), // overwrite skus array so an empty array is not required.
// });
// export const InventoryTransactionProductModifySchema = InventoryDbProductSchema.extend({
//   transactionDataType: z.literal('Inventory.Products.Modify'),
//   skus: z.array(InventoryDbSKUSchema).optional(),
// });
// export const InventoryTransactionProductSchema = InventoryDbProductSchema.extend({
//   transactionDataType: z.discriminatedUnion('transactionDataType', [
//     InventoryTransactionProductCreateSchema,
//     InventoryTransactionProductModifySchema,
//   ]),
// });
// // this is used for typing transactions and extracting data of any type of product transaction
// export type InventoryTransactionProduct = z.infer<typeof InventoryTransactionProductSchema>;
