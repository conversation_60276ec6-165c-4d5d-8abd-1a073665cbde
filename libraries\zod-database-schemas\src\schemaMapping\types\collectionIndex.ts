import { z } from 'zod/v4';

// First, define the IndexDirectionSchema
export const IndexDirectionSchema = z.union([
  z.number(),
  z.literal('2d'),
  z.literal('2dsphere'),
  z.literal('text'),
  z.literal('geoHaystack'),
  z.literal('hashed'),
]);

// For the Map case
const isMapWithStringKeyAndIndexDirection = (value: unknown): boolean => {
  if (!(value instanceof Map)) return false;

  for (const [key, val] of value.entries()) {
    if (typeof key !== 'string') return false;
    if (
      typeof val !== 'number' &&
      val !== '2d' &&
      val !== '2dsphere' &&
      val !== 'text' &&
      val !== 'geoHaystack' &&
      val !== 'hashed'
    ) {
      return false;
    }
  }

  return true;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- limited by zod requirements
const MapStringIndexDirectionSchema = z.custom<Map<string, any>>(isMapWithStringKeyAndIndexDirection, {
  message: 'Expected Map<string, IndexDirection>',
});

// Break the circular reference by removing the explicit type annotation
export const IndexSpecificationSchema = z.lazy(() =>
  z.union([
    z.string(),
    z.tuple([z.string(), IndexDirectionSchema]),
    z.record(z.string(), IndexDirectionSchema),
    MapStringIndexDirectionSchema,
    z
      .array(
        z.union([
          z.string(),
          z.tuple([z.string(), IndexDirectionSchema]),
          z.record(z.string(), IndexDirectionSchema),
          MapStringIndexDirectionSchema,
        ]),
      )
      .readonly(),
  ]),
);

// Create the rest of the schemas
export const CreateIndexesOptionsSchema = z.object({
  name: z.string().optional(),
  unique: z.boolean().optional(),
  sparse: z.boolean().optional(),
  expireAfterSeconds: z.number().optional(),
});

export const CollectionIndexSchema = z.object({
  key: IndexSpecificationSchema,
  options: CreateIndexesOptionsSchema.optional(),
});

// Now derive the types from the schemas
export type IndexDirection = z.infer<typeof IndexDirectionSchema>;
export type IndexSpecification = z.infer<typeof IndexSpecificationSchema>;
export type CreateIndexesOptions = z.infer<typeof CreateIndexesOptionsSchema>;
export type CollectionIndex = z.infer<typeof CollectionIndexSchema>;
