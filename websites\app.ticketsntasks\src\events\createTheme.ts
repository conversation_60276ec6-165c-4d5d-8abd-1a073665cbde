import { createTheme as createMuiTheme } from '@mui/material';

import type { Theme } from '@mui/material';
import type { ThemeSchema } from '@tnt/zod-client-schemas';
import type { z } from 'zod/v4';

type TntTheme = z.infer<typeof ThemeSchema>;

import '../modules/theme';

export const createTheme = (theme: TntTheme): Theme => {
  return createMuiTheme({
    palette: theme?.palette,
    typography: JSON.parse(JSON.stringify(theme.typography)) ?? {}, // Match proto type
  });
};
