{"name": "@tnt/app-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "tsc -b && vite build", "createEnv": "tsx ./scripts/createEnv.ts", "dev": "vite", "prebuild": "pnpm run createEnv", "preview": "vite preview", "test": "jest", "lint": "eslint ."}, "sideEffects": false, "dependencies": {"@connectrpc/connect": "^2.0.2", "@connectrpc/connect-web": "^2.0.2", "@dagrejs/dagre": "^1.1.4", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@floating-ui/dom": "^1.7.1", "@floating-ui/react": "^0.27.12", "@fontsource/roboto": "^5.2.6", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@tnt/env-loader": "workspace:*", "@tnt/error-logger": "workspace:*", "@tnt/protos-gen": "workspace:*", "@tnt/react-hooks": "workspace:*", "@tnt/zod-client-schemas": "workspace:*", "@tnt/zod-database-schemas": "workspace:*", "@tnt/zod-forms": "workspace:*", "@xyflow/react": "^12.7.1", "@zitadel/react": "1.0.5", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "oidc-client-ts": "3.1.0", "path": "^0.12.7", "pino": "9.6.0", "rambda": "9.4.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "7.54.2", "react-router-dom": "7.1.1", "vite-plugin-environment": "1.1.3", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "9.13.0", "@jest/globals": "29.7.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@tnt/create-env-file": "workspace:*", "@tnt/eslint-config": "workspace:*", "@tnt/typescript-config": "workspace:*", "@types/jest": "29.5.14", "@types/node": "22.7.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "identity-obj-proxy": "3.0.0", "nodemon": "3.1.7", "rollup-plugin-visualizer": "5.14.0", "ts-jest": "29.3.2", "ts-node": "10.9.2", "tsx": "4.19.2", "typescript": "^5.6.3", "typescript-eslint": "^8.0.1", "vite": "^6.3.5", "vite-plugin-node-polyfills": "0.23.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}}