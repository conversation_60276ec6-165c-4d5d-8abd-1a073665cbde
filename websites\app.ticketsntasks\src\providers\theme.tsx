import { Logger } from '@tnt/error-logger';
import { useContext, useEffect, useState } from 'react';
import { ThemeService } from '@tnt/protos-gen/lib/__generated__/tnt';
import { useConnectWebRpc } from '@tnt/react-hooks';

import { ThemeContext } from '../context';
import { defaultTheme, VITE_SERVICE_HOST, VITE_SERVICE_PORT } from '../constants';

import type { ThemeSchema } from '@tnt/zod-client-schemas';
import type { z } from 'zod/v4';
import type { ReactNode } from 'react';

type Theme = z.infer<typeof ThemeSchema>;

export const ThemeProvider = ({ children }: { children: ReactNode }): JSX.Element | null => {
  const contextTheme = useContext(ThemeContext);
  const [inflight, setInflight] = useState(false);
  const [theme, setTheme] = useState<Theme | undefined>(contextTheme);

  const themeService = useConnectWebRpc(ThemeService, `http://${VITE_SERVICE_HOST}:${VITE_SERVICE_PORT}`);
  // use a useEffect to get the theme and set it in the state
  useEffect(() => {
    const logger = new Logger({
      serviceName: 'tickets-n-tasks',
    });
    const getTheme = async (): Promise<void> => {
      try {
        const appTheme = await themeService.getTheme({ Id: 'APPSTHEM679683c3cc7905d32706e355' });
        if (!appTheme) {
          return;
        }
        setTheme({
          _id: appTheme.Id,
          themeName: appTheme.themeName,
          layout: appTheme.layout || { gridTemplateAreas: '', gridTemplateColumns: '', gridTemplateRows: '' },
          overrides: appTheme.overrides,
          palette: appTheme.palette,
          typography: appTheme.typography ? structuredClone(appTheme.typography) : defaultTheme.typography,
          zones: appTheme.zones,
        });
      } catch (err) {
        logger.error(`getTheme error`, { err });
        setTheme(defaultTheme);
      } finally {
        setInflight(false);
      }
    };

    if (!themeService) {
      return;
    }
    if (!theme && !inflight) {
      setInflight(true);
    }

    if (!theme && inflight) {
      getTheme().catch(err => {
        logger.error(`getTheme error`, { err });
        setTheme(defaultTheme);
      });
    }
  }, [theme, themeService, inflight, contextTheme]); // Added missing dependencies

  if (!theme) {
    return null;
  }

  return <ThemeContext.Provider value={theme}>{children}</ThemeContext.Provider>;
};
