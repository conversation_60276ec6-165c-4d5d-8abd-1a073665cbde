import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId } from '../../../../common';
import { ContactSchema } from '../../../../resources';

// no attached collection. use for typing and validating (parsing)
export const ScheduleDatesSchema = z.object({
  startDateEstimated: preprocessDate,
  startDateActual: preprocessDate.optional().nullable().default(null),
  completedDateEstimated: preprocessDate,
  completedDateActual: preprocessDate.optional().nullable().default(null),
  dueDate: preprocessDate,
  durationEstimated: z.number().optional().nullable().default(null),
  durationActual: z.number().optional().nullable().default(null),

  // validate that these would cover precedence
  // z.enum(['FINISH_TO_START', 'START_TO_START', 'FINISH_TO_FINISH', 'START_TO_FINISH']);

  // In project management, "precedence" refers to the order in which tasks must be completed,
  // meaning one activity needs to finish before another can start; while "lead" and "lag"
  // are specific durations within that dependency, where "lead" allows a following task to
  // begin before the previous one is completely finished (overlapping activities), and "lag"
  // forces a delay between tasks, requiring a specific amount of time to pass before the next
  // task can start

  // these are calculated values based on the lead/lag numbers for the project
  earlyStart: z.number().optional().nullable().default(null),
  earlyFinish: z.number().optional().nullable().default(null),
  lateStart: z.number().optional().nullable().default(null),
  lateFinish: z.number().optional().nullable().default(null),

  // these are entered values of work contraints based on the physical process itself
  lead: z.number().optional().nullable().default(null),
  lag: z.number().optional().nullable().default(null),
});

export const ScheduleSchema = z.object({
  _id: preprocessObjectId,
  createdBy: ContactSchema,
  createdOn: preprocessDate,
  dates: ScheduleDatesSchema,
  isActive: z.boolean(),
});
