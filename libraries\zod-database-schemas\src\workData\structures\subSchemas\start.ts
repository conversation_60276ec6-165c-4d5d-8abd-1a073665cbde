import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../../common';
import { ContactSchema } from '../../../resources';

import { StructureObjectBaseSchema } from './base';

// should contain and allow linking of information/documents/... for the entire project
// should be called structureRootReferenceId in referencing documents
// project inventory -- referenced to this objectId in the work data entries and inventory location where applicable
// project documents, ... referenced to this objectId in the work data entries and iamLinks where applicable
export const StructureObjectStartSchema = StructureObjectBaseSchema.extend({
  structureObjectType: z.literal('structureObjectStart'),
  projectSponsor: z.array(
    z.object({
      validFrom: preprocessDate,
      validTo: preprocessDate,
      title: preprocessObjectId32,
      contact: ContactSchema,
    }),
  ),
  projectLead: z.array(
    z.object({
      validFrom: preprocessDate,
      validTo: preprocessDate,
      title: preprocessObjectId32,
      contact: ContactSchema,
    }),
  ),
});

export type StructureObjectStart = z.infer<typeof StructureObjectStartSchema>;
