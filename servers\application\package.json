{"name": "@tnt/application-server", "type": "module", "scripts": {"build": "tsc", "createEnv": "tsx ./scripts/createEnv.mjs", "dev": "nodemon", "lint": "eslint .", "test": "jest ./src"}, "devDependencies": {"@jest/globals": "29.7.0", "@tnt/create-env-file": "workspace:*", "@tnt/eslint-config": "workspace:*", "@tnt/typescript-config": "workspace:*", "@tnt/zod-client-schemas": "workspace:*", "@tnt/zod-database-schemas": "workspace:*", "@types/jest": "29.5.14", "@types/node": "22.8.7", "jest": "29.7.0", "nodemon": "3.1.7", "pino-pretty": "13.0.0", "ts-jest": "29.3.2", "ts-node": "10.9.2", "typescript": "5.6.3"}, "dependencies": {"@bufbuild/buf": "^1.0.0", "@bufbuild/protobuf": "^2.0.0", "@bufbuild/protoc-gen-es": "^2.0.0", "@connectrpc/connect": "^2.0.2", "@connectrpc/connect-fastify": "^2.0.2", "@connectrpc/connect-node": "^2.0.2", "@connectrpc/connect-web": "^2.0.2", "@dotenv-run/load": "1.3.6", "@fastify/cors": "8.5.0", "@tnt/env-loader": "workspace:*", "@tnt/error-logger": "workspace:*", "@tnt/mongo-client": "workspace:*", "@tnt/protos-gen": "workspace:*", "@tnt/zod-client-schemas": "workspace:*", "fastify": "^4.28.1", "tsx": "4.19.2", "zod": "^3.25.67"}}