// import { z } from 'zod/v4';
// import { preprocessObjectId32 } from '../../common';

// import { preprocessObjectId } from '../../preprocessFunctions';
// import { InventoryDbStockLocationSchema } from '../commandSchemas/stockLocation';

// export const InventoryTransactionStockLocationCreateSchema = InventoryDbStockLocationSchema.extend({
//   transactionDataType: z.literal('Inventory.StockLocations.Create'),
//   productId: preprocessObjectId32,
//   skuId: preprocessObjectId,
// });
// export const InventoryTransactionStockLocationModifySchema = InventoryDbStockLocationSchema.extend({
//   transactionDataType: z.literal('Inventory.StockLocations.Modify'),
//   productId: preprocessObjectId32,
//   skuId: preprocessObjectId,
// });
// export const InventoryTransactionStockLocationSchema = InventoryDbStockLocationSchema.extend({
//   transactionDataType: z.enum(['Inventory.StockLocations.Create', 'Inventory.StockLocations.Modify']),
//   productId: preprocessObjectId32,
//   skuId: preprocessObjectId,
// });
// // this is used for typing transactions and extracting data of any type of stockLocation transaction
// export type InventoryTransactionStockLocation = z.infer<typeof InventoryTransactionStockLocationSchema>;
