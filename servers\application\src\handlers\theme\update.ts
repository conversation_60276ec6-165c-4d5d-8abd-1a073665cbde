import { Logger } from '@tnt/error-logger';
import { SchemaMap } from '@tnt/zod-database-schemas';
import { baseCRUDOperations } from '@tnt/mongo-client';

import type { ThemeSchema } from '@tnt/zod-database-schemas';
import type { UpdateThemeRequest } from '@tnt/protos-gen/lib/__generated__/tnt';
import type { z } from 'zod/v4';

type Theme = z.infer<typeof ThemeSchema>;

export const updateTheme = async (req: UpdateThemeRequest): Promise<Theme> => {
  const logger = new Logger({ serviceName: '@tnt/application-server' });
  const { Id, ...rest } = req;

  const themeOperations = new baseCRUDOperations<Theme>(SchemaMap.Application_Theme);

  const updatedTheme = await themeOperations
    .updateOne(Id, {
      themeName: rest.themeName,
      layout: rest.layout,
      palette: rest.palette ? JSON.parse(JSON.stringify(rest.palette)) : undefined,
      typography: rest.typography ? JSON.parse(JSON.stringify(rest.typography)) : undefined,
      zones: rest.zones,
    })
    .catch((err: Error) => {
      logger.error(`Theme:updateTheme: err: ${err}`);
      throw err;
    });

  if (!updatedTheme) {
    logger.error(`Theme:updateTheme: error updating theme`);
  }

  return {
    _id: updatedTheme._id,
    themeName: rest.themeName ?? 'Default Theme Name',
    layout: rest.layout ?? { gridTemplateAreas: '', gridTemplateColumns: '', gridTemplateRows: '' },
    palette: rest.palette ? JSON.parse(JSON.stringify(rest.palette)) : undefined,
    typography: rest.typography
      ? JSON.parse(JSON.stringify(rest.typography))
      : { fontFamily: '', fontSize: 0 },
    zones: rest.zones,
  };
};
