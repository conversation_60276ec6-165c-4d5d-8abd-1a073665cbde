import { z } from 'zod/v4';

import { StructureObjectWorkSchema } from './subSchemas/work';
import { StructureObjectDecisionSchema } from './subSchemas/decision';
import { StructureObjectEndSchema } from './subSchemas/end';
import { StructureObjectStartSchema } from './subSchemas/start';
import { StructureObjectBaseSchema } from './subSchemas/base';
import {
  StructureObjectReferenceTargetSchema,
  StructureObjectReferenceSourceSchema,
} from './subSchemas/reference';

// StructureObjectSchema utilizes a discriminated union to represents objects such as Start/End/Container/Decision/IO/Path
// with their specific object type data
// this schema is used for database read/write/parsing, it should not be referred or used in client schemas
// at all as schema.property.shape is not available and therefore it is unable to provide describe for metadata

// consider z.strict() or z.strictObject() to error if extra fields?
export const StructureObjectSchema = z.discriminatedUnion('structureObjectType', [
  StructureObjectStartSchema,
  StructureObjectDecisionSchema,
  StructureObjectBaseSchema,
  StructureObjectWorkSchema,
  StructureObjectEndSchema,
  StructureObjectReferenceTargetSchema,
  StructureObjectReferenceSourceSchema,
]);

export type StructureObject = z.infer<typeof StructureObjectSchema>;
