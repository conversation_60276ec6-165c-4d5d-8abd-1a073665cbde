import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Button, Checkbox, FormControlLabel, List, TextField } from '@mui/material';
import { Logger } from '@tnt/error-logger';

import { getSchemaMeta } from './utils/getSchemaMeta';

import type { FieldValues } from 'react-hook-form';
// import type { ZodTypeAny } from 'zod/v4';
import type { ZodType } from 'zod/v4';
import type { SchemaMeta } from './types';

// return a react form component
export const SchemaFormComponent = ({
  defaultValues,
  formName,
  onSubmit,
  schema,
}: {
  defaultValues?: FieldValues;
  formName: string;
  onSubmit: (data: FieldValues) => FieldValues;
  schema: ZodType;
}): React.JSX.Element => {
  const logger = new Logger({ serviceName: '@tnt/zod-forms:SchemaFormComponent' });

  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
  } = useForm({
    defaultValues,
    resolver: zodResolver(schema),
  });

  const schemaMeta = getSchemaMeta(schema);

  const RenderField = ({ field }: { field: SchemaMeta }): React.JSX.Element | null => {
    if (field.meta.type === 'object') {
      return (
        <Box key={field.name} sx={{ ml: 2, mt: 2 }}>
          <strong>{field.name}</strong> {/* Label for the parent group */}
          {field.children?.map(child => <RenderField key={child.name} field={child} />)}
        </Box>
      );
    }
    switch (field.meta.type) {
      case 'checkbox':
        return (
          <Controller
            key={field.name}
            name={field.name}
            control={control}
            render={({ field: controllerField }) => (
              <FormControlLabel
                control={<Checkbox {...controllerField} checked={!!controllerField.value} />}
                label={field.name} // Field label
              />
            )}
          />
        );
      case 'number':
        return (
          <TextField
            key={field.name}
            label={field.name} // Field label
            type={field.meta.type}
            required={field.meta.required}
            error={!!errors[field.name]}
            helperText={(errors[field.name]?.message as string) ?? ''}
            fullWidth
            margin='normal'
            {...register(field.name, { required: field.meta.required, valueAsNumber: true })}
          />
        );
      case 'json':
      case 'array':
      case 'map':
        return (
          <Controller
            key={field.name}
            name={field.name}
            control={control}
            render={({ field: controllerField }) => (
              <TextField
                label={field.name} // Field label
                multiline
                minRows={4}
                required={field.meta.required}
                error={!!errors[field.name]}
                helperText={(errors[field.name]?.message as string) ?? ''}
                fullWidth
                margin='normal'
                defaultValue={JSON.stringify(controllerField.value, null, 2)}
                onChange={e => controllerField.onChange(JSON.parse(e.target.value))}
              />
            )}
          />
        );
      case 'textbox':
        return (
          <TextField
            key={field.name}
            disabled={field.name === '_id'}
            hidden={field.name === '_id'}
            hiddenLabel={field.name === '_id'}
            label={field.name} // Field label
            type={field.meta.type}
            required={field.meta.required}
            error={!!errors[field.name]}
            helperText={(errors[field.name]?.message as string) ?? ''}
            fullWidth
            margin='normal'
            {...register(field.name, { required: field.meta.required })}
          />
        );
      default:
        return null;
    }
  };

  const handleFormSubmit = (data: FieldValues): void => {
    logger.info(`handleFormSubmit:data:`, data);
    if (onSubmit) {
      onSubmit(data);
    }
  };

  const handleInvalidForm = (invalidFormErrors: FieldValues): void => {
    logger.error(`handleInvalidForm:errors:`, invalidFormErrors);
  };

  return (
    <form name={formName} id={formName} onSubmit={handleSubmit(handleFormSubmit, handleInvalidForm)}>
      {Object.keys(errors).length > 0 && (
        <Box sx={{ color: 'error.main', mb: 2 }}>
          <strong>Form Errors:</strong>
          <List>
            {Object.entries(errors).map(([fieldName, error]) => (
              <li key={fieldName}>
                {fieldName}: {(error?.message as string) ?? ''}
              </li>
            ))}
          </List>
        </Box>
      )}
      {/* runtime line errors out on fieldMeta has type any? */}
      {schemaMeta.map(fieldMeta => (
        <RenderField key={fieldMeta.name} field={fieldMeta} />
      ))}
      <Button type='submit' variant='contained'>
        Submit
      </Button>
    </form>
  );
};
