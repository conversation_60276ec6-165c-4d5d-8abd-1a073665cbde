// pnpm -F @tnt/sandbox exec tsx -r @dotenv-run/load .\src\getInventoryTesting\index.ts
// pnpm -F @tnt/sandbox exec tsx .\src\getInventoryTesting\index.ts
import { ZodTypeAny } from 'zod/v4';
import { inspect } from 'util';
import { InventoryReadOperations } from '@tnt/mongo-client/src/inventoryOperations/inventoryReadOperations';
import { SchemaMap, SchemaMapItemType } from '@tnt/zod-database-schemas';
import {
  CursorOptions,
  PaginationOptions,
  ProductFilterParams,
  StockLocationFilterParams,
} from '@tnt/mongo-client';

(async () => {
  console.log('Starting retrieval');
  const productSchemaMap: SchemaMapItemType<ZodTypeAny, string> = SchemaMap.Inventory_Products;
  const invOps = new InventoryReadOperations();

  const cursorOptions: CursorOptions = {
    useCursor: false,
    batchSize: 1000,
  };

  const pagiOptions: PaginationOptions = {
    page: 2,
    limit: 5,
  };
  let pFilterParams: ProductFilterParams = {
    productId: 'INV_PROD67852017e02c4b52ccdf0657',
    skuId: '678559e27c7174c2f79b2758',
    stockLocationId: '67898ba65d671c147fbc0d87',
    itemIds: { id1: 'id100', id2: 'id102', id3: 'id103' },
  };

  const slFilterParams: StockLocationFilterParams = {
    //   productId: 'INV_PROD67852017e02c4b52ccdf0657',
    // skuId: '678559e27c7174c2f79b2758',
    stockLocationId: '67898ba65d671c147fbc0d87',
    // itemIds: { id1: 'id100', id2: 'id102', id3: 'id103' },
  };

  pFilterParams = {
    productId: 'INV_PROD67852017e02c4b52ccdf0657',
    skuId: '678559e27c7174c2f79b2758',
    // stockLocationId: '67898ba65d671c147fbc0d87',
    itemIds: { id1: 'id100', id2: 'id102', id3: 'id103' },
  };

  // const retvalPR = await invOps.getFilteredProduct(pFilterParams, { paginationOptions: pagiOptions });
  const retvalPR = await invOps.getFilteredProduct(pFilterParams);

  console.dirxml(inspect(retvalPR, { depth: 6 }));

  // const retValStock = await invOps.getFilteredStockLocation(slFilterParams);

  // if (!retValStock) {
  //   throw new Error(`retvalStock null`);
  // }

  console.log('.... process exiting');
  process.exit(0); // Exit with code 0 (success)
})();
