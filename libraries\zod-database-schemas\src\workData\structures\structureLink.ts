import { z } from 'zod/v4';

// const EDGE_TYPE = z.enum(['oneWayArrow', 'twoWayArrow', 'transparent', 'dashed', 'thick']);
// const PRECEDENCE_TYPE = z.enum(['FINISH_TO_START', 'START_TO_START', 'FINISH_TO_FINISH', 'START_TO_FINISH']);

import { preprocessObjectId32 } from '../../common';
import { RootReferenceStructureObjectSchema } from './subSchemas/referenceStructureObject';

// the link is what ties a project together, it is the connection point that everything is tied to.
// in the instance structures, they need to relate to the overall project/process they are part of as well as the container
// they are contained within

// structure links do NOT confer permissions, access, ... that is only done by iamLinks.

export const StructureLinkSchema = z.object({
  _id: preprocessObjectId32,
  structureRootReference: RootReferenceStructureObjectSchema, // this should be the main process/project, if this is the root, reference itself
  sourceName: z.string(), // used for debug/test
  source: preprocessObjectId32, // object-container
  sourceHandle: z.string().optional().nullable().default(null),
  targetName: z.string(), // used for debug/test
  target: preprocessObjectId32, // subject-container
  targetHandle: z.string().optional().nullable().default(null),
  // edgeType: EDGE_TYPE.default(EDGE_TYPE.Values.oneWayArrow), // display usage only
  // relationship: PRECEDENCE_TYPE.default(PRECEDENCE_TYPE.Values.FINISH_TO_START), // display usage only, is this required to help define the visual type of path?
});

export type StructureLink = z.infer<typeof StructureLinkSchema>;
