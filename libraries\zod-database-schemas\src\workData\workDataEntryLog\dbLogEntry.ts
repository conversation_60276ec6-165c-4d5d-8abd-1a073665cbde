// should contain all database creates/modifications?
import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../common';
import { AuthUserSchema } from '../../authentication';

export const workDataEntryLogSchema = z.object({
  _id: preprocessObjectId32,
  database: z.string(),
  collection: z.string(),
  documentId: preprocessObjectId32,
  datetimeStamp: z.date(),
  madeBy: AuthUserSchema,
  function: z.enum(['create', 'delete', 'update']),
  data: z.any(),
});
