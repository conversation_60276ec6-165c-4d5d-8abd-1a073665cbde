import { z } from 'zod/v4';

import { JsonValueSchema } from '../../common';

import { CollectionIndexSchema } from './collectionIndex';

// export type MongoDbBackupFileType = {
//   dbName: string;
//   collectionName: string;
//   schemaName: string;
//   availableActions: string[];
//   action: string;
//   indexes: Array<CollectionIndex>;
//   data: JsonValue[];
// };

export const MongoDbBackupFileSchema = z.object({
  dbName: z.string(),
  collectionName: z.string(),
  schemaName: z.string(),
  availableActions: z.array(z.string()),
  action: z.string(),
  indexes: z.array(CollectionIndexSchema),
  data: z.array(JsonValueSchema),
});

export type MongoDbBackupFile = z.infer<typeof MongoDbBackupFileSchema>;
