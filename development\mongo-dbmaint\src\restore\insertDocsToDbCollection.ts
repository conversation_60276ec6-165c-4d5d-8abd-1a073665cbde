import { baseCRUDOperations } from '@tnt/mongo-client';

import { mongoCRUDCheckReturnForError } from './errorHandler';

import type { z } from 'zod/v4';
import type { JsonValue, SchemaMapItemType } from '@tnt/zod-database-schemas';
// TODO M-1:  figure out error handling.

export async function insertDocsToDbCollection<T extends z.ZodTypeAny, R extends string>(
  smi: SchemaMapItemType<T, R>,
  data: JsonValue[],
  dbAction: string,
): Promise<boolean> {
  const thisCollection = new baseCRUDOperations(smi);

  // insert/delete/append data
  // TODO M-1 standardize return values
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- the returned values are different depending on the type, should be standardized later
  let retValue: any;

  // TODO M-1: needs some work on error handling, but leaving until <PERSON> gives some direction
  switch (dbAction) {
    case 'delete':
      retValue = await thisCollection.deleteAll();
      break;
    case 'append':
      retValue = await thisCollection.parseInsert(data);
      break;
    case 'delete-insert':
      if ((await thisCollection.deleteAll()) === true) {
        retValue = await thisCollection.parseInsert(data);
      } else {
        retValue = false;
      }
      break;
    default:
      retValue = false;
      console.error('❗  restore.insertDocsToDbCollection.dbAction not found');
      break;
  }

  // );
  if (retValue !== false && mongoCRUDCheckReturnForError(retValue) !== false) {
    // good data
    // console.log(
    //   ' ******************** ${typedData.dbName}.${typedData.schemaName} data seed success ******************** '
    // );
    return true;
  } else {
    return false;
  }
}
