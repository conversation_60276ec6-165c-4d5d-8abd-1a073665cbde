{"name": "@tnt/shared-enums", "version": "1.0.0", "description": "", "main": "lib/index.js", "type": "module", "scripts": {"prebuild": "tsc", "lint": "eslint ."}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@dotenv-run/load": "1.3.6", "@tnt/eslint-config": "workspace:*", "@tnt/shared-scripts": "workspace:*", "@tnt/typescript-config": "workspace:*", "nodemon": "3.1.7", "ts-node": "10.9.2", "tsx": "4.19.2", "typescript": "^5.6.3"}, "dependencies": {"zod": "^3.25.67"}}