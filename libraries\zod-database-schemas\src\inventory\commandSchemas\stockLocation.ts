import { z } from 'zod/v4';

import { preprocessObjectId, preprocessObjectId32 } from '../../common';

import { InventoryDbItemSchema } from './item';
import { ScheduledTransactionSchema } from './scheduledTransaction';

// Sum of items/products/... in a specific location (includes an array to determine status)
// cannot provide sums for individual status types since scheduled includes a date/time stamp
export const InventoryDbStockLocationSchema = z.object({
  _id: preprocessObjectId,
  active: z.boolean(), // used to deactivate old locations
  // itemLocation can be a project/container so that it is only available in that project/container (WIP)
  locationId: preprocessObjectId32, // InventoryLocationSchema(INV_LOCA),
  //price: InventoryPriceSchema.optional(),
  // either-or? for lotNumber-serialNumber? both?
  items: z.array(InventoryDbItemSchema).optional().nullable().default(null), // lot/serial numbers
  stockLocationSum: z.number().readonly().default(0),
  stockLocationReservedSum: z.number().readonly().default(0),
  scheduled: z.array(ScheduledTransactionSchema).optional().nullable().default(null),
  threshold: z.number().optional().nullable().default(null),
  target: z.number().optional().nullable().default(null),
  tags: z.array(z.string().optional().nullable().default(null)).optional().nullable().default([]),
});

export type InventoryDbStockLocation = z.infer<typeof InventoryDbStockLocationSchema>;
