import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId, preprocessObjectId32 } from '../../common';
import { AuthUserSchema } from '../../authentication';

// in an raf, the following properties are used
// in an raf entry, this will need to incorporate a further step of inventory insertion in orde to obtain the rafItemInventoryRef
export const RafItemSchema = z.object({
  assemblyDefType: z.literal('raf'),
  // items: z.array(
  //   z.object({
  _id: preprocessObjectId,
  createdById: AuthUserSchema,
  productId: preprocessObjectId32,
  skuId: preprocessObjectId,
  itemName: z.string(),
  itemDescription: z.string(),
  itemNotes: z.string(),
  qtyValidation: z.enum(['Required', 'Requested', 'Estimated']),
  qty: z.number().default(0),
  workDataEntries: z
    .array(
      z.object({
        _id: preprocessObjectId,
        enteredBy: AuthUserSchema,
        timestamp: preprocessDate,
        inventoryTransactionId: preprocessObjectId32,
        good: z.number().default(0),
        bad: z.number().default(0),
        indeterminate: z.number().default(0),
      }),
    )
    .optional()
    .nullable()
    .default(null),

  // itemStatus: preprocessObjectId32,  // is this needed for QC?
  // ItemAttachedResources: z.array(preprocessObjectId32),  // these should probably be attached by an IAMLink? not here?
  //   }),
  // ),
});
