// Do we need the protobuf Timestamp type here?
// De we have a site id, or a client id?
import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../../common';
import { JsonSchema } from '../../../common/json';
import { JsonValueSchema } from '../../../common/functions';

import { LayoutSchema } from './layout';

export const ThemeSchema = z.object({
  _id: preprocessObjectId32,
  themeName: z.string(),
  layout: LayoutSchema,
  overrides: z.array(z.string()).optional(),
  palette: JsonSchema,
  typography: JsonValueSchema,
  spacing: z.number().optional(),
  zones: JsonSchema.optional(),
});

export type Theme = z.infer<typeof ThemeSchema>;
