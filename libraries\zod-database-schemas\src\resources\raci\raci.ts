import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../common';
import { GroupHistoricalSchema } from '../group/group';
import { ContactSchema } from '../contact/contact';

// RACI - responsible, accountable, consulted, informed
// used in tickets/tasks to determine the ownership of the different actions required
// should be templated inside a project/process/job/task so the builder can choose by name
/*
export type raci = {
  isActive: mongoose.Schema.Types.Boolean;
  container: mongoose.ObjectId; // should reference the main object such as a project, process, task, path, ...
  subContainers: mongoose.ObjectId[]; // should reference contained object(s) such as job, task, path, ...
  name: mongoose.Schema.Types.String;
  responsible: group;
  accountable: contact;
  consulted: group;
  informed: group;
};
*/

// the objectId's of the chosen groups/contacts will be embedded in a wbObject during the build process
// But this schema object represents a templated
// version so that a builder can select based on a preconfigured setup in the database
export const RaciTrackingSchema = z.object({
  _id: preprocessObjectId32,
  name: z.string().optional().nullable().default(null),
  isActive: z.boolean().default(true),
  // the container represents an array of the top-most wbObects where this templated object
  // would be available for selection during the build process

  structureRootReferenceId: preprocessObjectId32.optional().nullable().default(null), // this should be the main process/project
  structureReferenceId: preprocessObjectId32.optional().nullable().default(null), // should reference the main object such as a project, process, task, path, ...
  //containerId: z.array(preprocessObjectId32).optional(), // this is the specific structure container in that main process/project
  responsible: z.array(preprocessObjectId32), // ref:"Group", used to represent say in a ticket, that group doing the work (members)
  accountable: z.object({ contact: preprocessObjectId32, title: z.string().default('accountable') }), // ref:"Contact"},
  consulted: z.array(preprocessObjectId32), // ref:"Group",
  informed: z.array(preprocessObjectId32), // ref:"Group",
});

export type RaciTracking = z.infer<typeof RaciTrackingSchema>;

// it is expected that over time, group membership will change, once a project has started, the group fields that would
// want to later be referenced to determine who worked on it, would need to reflect the actual contacts at that time
// therefore these should be written as string arrays when container is closed.
// should this include validFrom/validTo and be implemented as an array in the containers?
export const RaciHistoricalSchema = z.object({
  responsible: z.array(GroupHistoricalSchema), // ref:"Group", used to represent say in a ticket, that group doing the work (members)
  // account is a contact, only a single person can be held accountable and is allowed to sign off on the work
  // title is used to allow for things like Scrum Master and such that can be defined for only a single task.
  accountable: z.object({ title: z.string().default('accountableContact'), contact: ContactSchema }), // ref:"Contact"},
  consulted: z.array(GroupHistoricalSchema), // ref:"Group",
  informed: z.array(GroupHistoricalSchema), // ref:"Group",
});

// Export the schema to create a "Contact" model class that be used directly
export type Raci = z.infer<typeof RaciHistoricalSchema>;
