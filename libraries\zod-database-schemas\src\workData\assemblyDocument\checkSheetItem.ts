import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId } from '../../common';
import { AuthUserSchema } from '../../authentication';

// in a checksheet, the following properties are used
export const CheckSheetItemSchema = z.object({
  assemblyDefType: z.literal('checksheet'),
  // items: z.array(
  //   z.object({
  _id: preprocessObjectId,
  name: z.string(),
  description: z.string(),
  notes: z.string(),
  qtyValidation: z.enum(['Required', 'Requested', 'Estimated']),
  qty: z.number().default(0),
  workDataEntries: z
    .array(
      z.object({
        _id: preprocessObjectId,
        enteredBy: AuthUserSchema,
        timestamp: preprocessDate,
        good: z.number().default(0),
        bad: z.number().default(0),
        indeterminate: z.number().default(0),
      }),
    )
    .optional()
    .nullable()
    .default(null),

  // itemStatus: preprocessObjectId32,
  // ItemAttachedResources: z.array(preprocessObjectId32),  // these should probably be attached by an IAMLink? not here?
  //   }),
  // ),
});
