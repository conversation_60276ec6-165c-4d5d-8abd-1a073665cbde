import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';
import { PhoneSchema } from '../phone';

// distinctive organization that also requires an separate address
// typically used for a separate warehouse, branch, or an external organization
export const EntitySchema = z.object({
  _id: preprocessObjectId32,
  isActive: z.boolean(),
  accountNum: z.string(),
  name: z.string(),
  description: z.string(),
  legal_designation: z.string(),
  type: preprocessObjectId32, // LLC, Scorp, Ccorp, Private, Department, Team, Warehouse ...
  parent: preprocessObjectId32.optional().nullable().default(null), // typically another entity
  address: preprocessObjectId32,
  phone: z.array(PhoneSchema),
  webURL: z.string(),
  createdBy: preprocessObjectId32,
  createdOn: preprocessDate,
});

export type Entity = z.infer<typeof EntitySchema>;
