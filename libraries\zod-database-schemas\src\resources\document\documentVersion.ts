import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId, preprocessObjectId32 } from '../../common';

export const DocumentVersionSchema = z.object({
  _id: preprocessObjectId, // since it is not a collection root document, it uses a standard objectId
  versionId: z.string().optional().nullable().default(null),
  versionName: z.string(),
  versionDescription: z.string(),
  filePathURL: z.string().url(), // file name is defined by version
  fileType: preprocessObjectId32,
  tags: z.array(z.string()),
  createdBy: preprocessObjectId32,
  createdOn: preprocessDate,
  validFrom: preprocessDate,
  validTo: preprocessDate,
});

export const ResourceEntryDocumentSchema = DocumentVersionSchema.extend({
  transactionDataType: z.literal('document'),
});
