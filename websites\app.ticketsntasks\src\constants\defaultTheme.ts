import type { ThemeSchema } from '@tnt/zod-client-schemas';
import type { z } from 'zod/v4';

type Theme = z.infer<typeof ThemeSchema>;

export const defaultTheme: Theme = {
  _id: 'APPSTHEM679683c3cc7905d32706e355',
  themeName: 'Main',
  layout: {
    gridTemplateAreas: '"header header header""aside content tools""footer footer footer"',
    gridTemplateColumns: 'auto 1fr auto',
    gridTemplateRows: 'auto 1fr auto',
  },
  palette: {
    primary: {
      main: '#0047AB',
      light: '#3D73C1',
      dark: '#003080',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#FFC857',
      dark: '#D89C1D',
      contrastText: '#1E293B',
    },
    background: {
      default: '#F9FAFB',
      paper: '#FFFFFF',
    },
    grey: {
      100: '#F3F4F6',
      500: '#6B7280',
      700: '#374151',
      900: '#111827',
    },
    success: { main: '#2E7D32' },
    error: { main: '#D32F2F' },
    warning: { main: '#ED6C02' },
    info: { main: '#0288D1' },
  },
  typography: {
    fontFamily: 'Inter, sans-serif',
    h1: { fontSize: '2rem', fontWeight: 600 },
    h2: { fontSize: '1.5rem', fontWeight: 600 },
    body1: { fontSize: '1rem' },
    body2: { fontSize: '0.875rem' },
    button: { fontWeight: 600 },
  },
  zones: {
    header: {
      visible: true,
      fill: '#DDDDDD',
    },
  },
  overrides: [],
};

/*
palette: {
    primary: {
      main: '#0047AB',
      light: '#3D73C1',
      dark: '#003080',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#FFC857',
      dark: '#D89C1D',
      contrastText: '#1E293B',
    },
    background: {
      default: '#F9FAFB',
      paper: '#FFFFFF',
    },
    grey: {
      100: '#F3F4F6',
      500: '#6B7280',
      700: '#374151',
      900: '#111827',
    },
    success: { main: '#2E7D32' },
    error: { main: '#D32F2F' },
    warning: { main: '#ED6C02' },
    info: { main: '#0288D1' },
  },
  typography: {
    fontFamily: 'Inter, sans-serif',
    h1: { fontSize: '2rem', fontWeight: 600 },
    h2: { fontSize: '1.5rem', fontWeight: 600 },
    body1: { fontSize: '1rem' },
    body2: { fontSize: '0.875rem' },
    button: { fontWeight: 600 },
  },
*/
