{"name": "@tnt/zod-forms", "version": "1.0.0", "description": "", "main": "lib/index.js", "type": "module", "scripts": {"build": "tsc", "lint": "eslint ."}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@hookform/resolvers": "3.10.0", "@tnt/create-env-file": "workspace:*", "@tnt/eslint-config": "workspace:*", "@tnt/typescript-config": "workspace:*", "@types/jest": "29.5.12", "jest": "29.7.0", "jest-mock": "29.7.0", "jest-xml-matcher": "1.2.0", "nodemon": "3.1.7", "pino-pretty": "11.2.2", "supertest": "7.0.0", "ts-jest": "29.2.4", "ts-node": "10.9.2", "tsx": "4.19.2", "typescript": "^5.6.3"}, "dependencies": {"@mui/material": "6.3.1", "@tnt/error-logger": "workspace:*", "@tnt/zod-client-schemas": "workspace:*", "ejs": "3.1.10", "react": "^18.3.1", "react-hook-form": "7.54.2", "zod": "^3.25.67"}}