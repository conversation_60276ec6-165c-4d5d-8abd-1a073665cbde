// import { z } from 'zod/v4';
// import { InventoryDbLocationSchema } from '../commandSchemas';

// export const InventoryTransactionLocationCreateSchema = InventoryDbLocationSchema.extend({
//   transactionDataType: z.literal('Inventory.Locations.Create'),
// });
// export const InventoryTransactionLocationModifySchema = InventoryDbLocationSchema.extend({
//   transactionDataType: z.literal('Inventory.Locations.Modify'),
// });
// export const InventoryTransactionLocationSchema = InventoryDbLocationSchema.extend({
//   transactionDataType: z.enum(['Inventory.Locations.Create', 'Inventory.Locations.Modify']),
// });
// // this is used for typing transactions and extracting data of any type of item transaction
// export type InventoryTransactionLocation = z.infer<typeof InventoryTransactionLocationSchema>;
