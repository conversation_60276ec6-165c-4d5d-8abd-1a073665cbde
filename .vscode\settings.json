{"files.associations": {"turbo.json": "jsonc"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[javascript]": {"editor.codeActionsOnSave": ["source.fixAll"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[javascriptreact]": {"editor.codeActionsOnSave": ["source.fixAll"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[typescript]": {"editor.codeActionsOnSave": ["source.fixAll"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[typescriptreact]": {"editor.codeActionsOnSave": ["source.fixAll"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "eslint.workingDirectories": ["./apps", "./packages"], "eslint.useFlatConfig": true, "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "eslint.nodePath": "", "todo-tree.tree.scanMode": "workspace only", "csharp.format.enable": false}