import { getAllSchemaEntries } from '@tnt/zod-database-schemas';

import { MongoDBConnection } from './dbConnection';

import type { CollectionIndex } from '@tnt/zod-database-schemas';
import type { Collection } from 'mongodb';

export async function dbConnectionInit(): Promise<void> {
  // intialize db Connection
  const schemas = getAllSchemaEntries();
  for (const smi of schemas) {
    const instance = MongoDBConnection.getInstance(smi);
    const db = await instance.connectDb(smi.serverUri);
    if (!instance.isConnected) {
      throw new Error(`Error connecting to database ${smi.dbName}`);
    }
    const collection: Collection = db.collection(smi.collectionName);

    // see rolling index builds on replica sets
    // https://www.mongodb.com/docs/manual/tutorial/build-indexes-on-replica-sets/
    // https://stackoverflow.com/questions/70446161/how-to-reliably-wait-for-an-index-to-be-ready-in-mongodb-with-the-node-js-driver

    // on a rebuild, this could take a LONG time, should be manually initiated process.
    // would it be faster to load the data after creating indexes?
    for (const { key, options } of smi.indexes as CollectionIndex[]) {
      await collection.createIndex(key, options || {});
      // check status of the process and it's completion is very well documented at docs.mongodb.com/manual/reference/method/db.currentOp/…
      //   db.command({"currentOp":....})
      console.log(`✔ Index ensured on ${smi.collectionName}:`, key);
    }
  }
}
