import type { z } from 'zod/v4';

// returns false if <PERSON>rro<PERSON> and prints error to console, else returns the value
export const mongoCRUDCheckReturnForError = (retValue: unknown): unknown => {
  if (retValue instanceof Error) {
    if (retValue.name === 'ZodError') {
      const err = retValue as z.ZodError;
      console.error('❗ mongoCRUD z.ZodError start -- Validation failed!');
      console.dirxml(err.issues, { depth: null });
      console.error('❗ mongoCRUD z.ZodError end -- Validation failed!');
      return false;
      // throw err;
    } else {
      const err = retValue;
      console.error('❗ mongoCRUD error start -- Validation failed!');
      console.dirxml(err.name + ': ' + err.message);
      console.dirxml(err.stack);
      console.error('❗ mongoCRUD error end -- Validation failed!');
      return false;
    }
  } else {
    return retValue;
  }
};
