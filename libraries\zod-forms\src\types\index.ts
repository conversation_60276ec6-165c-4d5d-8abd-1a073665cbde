import type { ZodFormsMeta } from '@tnt/zod-client-schemas';
import type { FieldValues, SubmitHandler } from 'react-hook-form';
import type { ZodSchema } from 'zod/v4';

export type FormProps<T extends FieldValues> = {
  schema: ZodSchema<T>;
  onSubmit: SubmitHandler<T>;
};

export type SchemaMeta_orig = {
  name: string;
  type: 'json' | 'text' | 'number' | 'checkbox' | 'select' | 'array' | 'object' | 'map';
  required: boolean;
  children?: SchemaMeta[]; // For nested schemas
};

export type SchemaMeta = {
  name: string;
  meta: ZodFormsMeta;
  children?: SchemaMeta[]; // For nested schemas
};
