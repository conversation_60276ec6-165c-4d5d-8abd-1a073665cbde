/*
 * Execute with the command
 * FROM powershell
 * pnpm -F @tnt/mongo-dbmaint exec tsx .\src\restore\index.ts
 * FROM Bash
 * pnpm -F @tnt/mongo-dbmaint exec tsx ./src/restore/index.ts
 *
 * With a filter and a package.json script -- then all you would have to do is change the path in the package.json
 * pnpm -F @tnt/mongo-dbmaint dev:restore
 */

import path from 'path';
import { fileURLToPath } from 'url';

import { fileLoop } from './fileLoop';

const js_filename = fileURLToPath(import.meta.url);
const js_dirname = path.dirname(js_filename);

const restoreFilesPath = path.normalize(path.join(js_dirname, '../restoreData'));

// (async () => {
console.log('Starting restoreLoop');
await fileLoop(restoreFilesPath);

console.log('.... process exiting');
process.exit(0); // Exit with code 0 (success)
// })();
