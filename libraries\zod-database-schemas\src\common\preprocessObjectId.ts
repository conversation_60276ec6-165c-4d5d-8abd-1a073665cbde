import { z } from 'zod/v4';
import { generateObjectId } from '@tnt/shared-utilities';

export const preprocessObjectId = z.preprocess(
  input => {
    if (input === null) return input; // allow null/undefined

    if (input === '' || input === 'newObjectId') {
      return generateObjectId();
    }

    if (
      input !== null &&
      typeof input === 'object' &&
      'toHexString' in input &&
      typeof input.toHexString === 'function'
    ) {
      return (input as { toHexString: () => string }).toHexString();
    }

    if (typeof input === 'string') {
      if (/^[0-9a-fA-F]{24}$/.test(input)) {
        return input;
      }
      return input; // invalid format, let schema catch it
    }

    return input; // invalid type, let schema catch it
  },
  z.string().regex(/^[0-9a-fA-F]{24}$/, {
    message: 'Expected a 24-character hex string for ObjectId',
  }),
);
