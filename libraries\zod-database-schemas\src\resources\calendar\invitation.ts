import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';
import { LocationSchema } from '../location';

// https://lldcoding.com/design-google-calendar-database-model

export const InvitationSchema = z.object({
  _id: preprocessObjectId32,
  eventId: preprocessObjectId32,
  location: LocationSchema,
  invited: z.array(preprocessObjectId32),
  senderId: preprocessObjectId32,
  status: z.string().default('pending'),
  sentAt: preprocessDate,
  response: z.string(),
});

export type Invitation = z.infer<typeof InvitationSchema>;
