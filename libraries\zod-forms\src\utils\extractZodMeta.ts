import { FormFieldTypes } from '@tnt/zod-client-schemas';
import { ZodArray, ZodBoolean, ZodDate, ZodNumber, ZodObject, ZodString } from 'zod/v4';

import type { ZodFormsMeta } from '@tnt/zod-client-schemas';
import type { ZodTypeAny } from 'zod/v4';

// Helper function to extract metadata from a schema field, should be located in client code?
export const extractMetadata = (zodSchemaField: ZodTypeAny): ZodFormsMeta => {
  // Default metadata based on schema type
  let defaultMeta: ZodFormsMeta;

  if (zodSchemaField instanceof ZodString) {
    defaultMeta = { type: FormFieldTypes.textBox, hidden: false };
  } else if (zodSchemaField instanceof ZodNumber) {
    defaultMeta = { type: FormFieldTypes.number, hidden: false };
  } else if (zodSchemaField instanceof ZodBoolean) {
    defaultMeta = { type: FormFieldTypes.checkbox, hidden: false };
  } else if (zodSchema<PERSON>ield instanceof ZodDate) {
    defaultMeta = { type: FormFieldTypes.date, hidden: false };
  } else if (zodSchemaField instanceof ZodObject) {
    defaultMeta = { type: FormFieldTypes.object, hidden: false };
  } else if (zodSchemaField instanceof ZodArray) {
    defaultMeta = { type: FormFieldTypes.array, hidden: false };
  } else {
    defaultMeta = { type: FormFieldTypes.unknown, hidden: true };
  }

  // If no description, return the default
  if (!zodSchemaField.description) {
    return defaultMeta;
  }

  // Try to parse the description
  try {
    const typedDesc = JSON.parse(zodSchemaField.description) as ZodFormsMeta;
    // Ensure the parsed result conforms to ZodFormsMeta structure
    // by merging with defaultMeta to ensure all required fields exist
    return {
      ...defaultMeta,
      ...typedDesc,
    };
  } catch {
    return defaultMeta; // Return default on error instead of null
  }
};

// Example usage
// const idMeta = extractMetadata(ZodSchemaTest.shape.id);
// console.log(idMeta);

// extracting metadata for all fields dynamically:
// Object.entries(ZodSchemaTest.shape).forEach(([key, fieldSchema]) => {
//   const meta = extractMetadata(fieldSchema);
//   console.log(`Field: ${key}, Metadata:`, meta);
// });
