/*
Logging to standard output (stdout) with <PERSON><PERSON> is considered a best practice
because it allows for maximum flexibility in how you collect and process logs,
adhering to the "12-factor app" principles by providing a single, well-defined
output stream that can be easily piped to different log aggregation tools or
processing pipelines without requiring changes within your application code;
this also leverages <PERSON><PERSON>'s high performance, making it efficient for large-scale logging scenarios.
https://betterstack.com/community/guides/logging/how-to-install-setup-and-use-pino-to-log-node-js-applications/
*/

import pino from 'pino';

import { LoggerOptions } from '../types';

export class Logger {
  private logger: pino.Logger;

  constructor(options: LoggerOptions) {
    this.logger = pino({
      name: options.serviceName,
      level: options.environment === 'production' ? 'info' : 'debug',
      formatters: {
        level: label => ({ level: label }),
      },
    });
  }

  info(message: string, meta: Record<string, unknown> = {}) {
    this.logger.info(meta, message);
  }

  warn(message: string, meta: Record<string, unknown> = {}) {
    this.logger.warn(meta, message);
  }

  debug(message: string, meta: Record<string, unknown> = {}) {
    this.logger.debug(meta, message);
  }
  // original
  // error(message: string, meta: Record<string, unknown> = {}) {
  //   this.logger.error(meta, message);
  // }
  // expanded to accept multiple signatures
  error(message: string, meta?: unknown): void {
    const logMeta: Record<string, unknown> = {};

    if (meta instanceof Error) {
      logMeta.name = meta.name;
      logMeta.message = meta.message;
      logMeta.stack = meta.stack;
    } else if (typeof meta === 'object' && meta !== null) {
      try {
        logMeta.meta = JSON.parse(JSON.stringify(meta));
      } catch {
        logMeta.meta = { error: 'Could not serialize meta object' };
      }
    } else if (typeof meta === 'string') {
      logMeta.meta = meta;
    } else if (meta !== undefined) {
      logMeta.meta = String(meta);
    }

    this.logger.error(logMeta, message);
  }
}
