import { z } from 'zod/v4';
import { ThemeSchema as BaseThemeSchema, SchemaMap } from '@tnt/zod-database-schemas';

import { createMetaJsonString, FormFieldTypes } from '../../../types/zodFormsMeta';

// First, extend the base schema with new properties
let ThemeSchema = BaseThemeSchema.extend({
  _id: z.string().refine(val => {
    return (
      val.length === 32 &&
      val.slice(0, 8) === SchemaMap.Application_Theme.dbId + SchemaMap.Application_Theme.collectionId
    );
  }),
});

// key point is that Zod methods like .describe() return new schema instances rather than modifying the original.
// therefore create a new schema with descriptions added to it
// use the shape of the current schema to avoid having to duplicate the base properties
ThemeSchema = z.object({
  ...ThemeSchema.shape,
  // Override only the properties you want to add descriptions to
  themeName: ThemeSchema.shape.themeName.describe(
    createMetaJsonString({
      type: FormFieldTypes.textBox,
      label: 'ThemeName Label',
      hidden: false,
      required: true,
    }),
  ),
  layout: ThemeSchema.shape.layout.describe(
    createMetaJsonString({
      type: FormFieldTypes.object,
      label: 'layout Label',
      hidden: false,
      required: true,
    }),
  ),
  overrides: ThemeSchema.shape.overrides.describe(
    createMetaJsonString({
      type: FormFieldTypes.array,
      label: 'Overrides Label',
      hidden: false,
      required: true,
    }),
  ),
  palette: ThemeSchema.shape.palette.describe(
    createMetaJsonString({
      type: FormFieldTypes.json,
      label: 'palette Label',
      hidden: false,
      required: true,
    }),
  ),
  spacing: ThemeSchema.shape.spacing.describe(
    createMetaJsonString({
      type: FormFieldTypes.number,
      label: 'spacing Label',
      hidden: false,
      required: false,
    }),
  ),
  typography: ThemeSchema.shape.typography.describe(
    createMetaJsonString({
      type: FormFieldTypes.json,
      label: 'palette Label',
      hidden: false,
      required: true,
    }),
  ),
  zones: ThemeSchema.shape.zones.describe(
    createMetaJsonString({
      type: FormFieldTypes.json,
      label: 'zones Label',
      hidden: false,
      required: true,
    }),
  ),
  // Add descriptions to other properties as needed...
});

// Export the new schema with modified properties and desired descriptions
export { ThemeSchema };
