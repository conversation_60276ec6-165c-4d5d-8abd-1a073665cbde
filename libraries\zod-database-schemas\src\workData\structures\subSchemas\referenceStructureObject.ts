import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../../common';

export const ReferenceStructureObjectSchema = z.object({
  containedById: preprocessObjectId32,
  containedByType: z.enum(['structureObjectStart', 'structureObjectBase']),
});
export const RootReferenceStructureObjectSchema = z.object({
  containedById: preprocessObjectId32,
  containedByType: z.enum(['structureObjectStart']),
});
export type ReferenceStructureObject = z.infer<typeof ReferenceStructureObjectSchema>;
export type RootReferenceStructureObject = z.infer<typeof RootReferenceStructureObjectSchema>;
