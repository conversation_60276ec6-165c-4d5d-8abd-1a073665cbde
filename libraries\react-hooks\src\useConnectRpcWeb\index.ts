import { useMemo } from 'react';
import { type DescService } from '@bufbuild/protobuf';
// TODO M-9999 how will this be implemented for node connections vs web connections?
// will we add the transport type to the parameters or create different connect functions?
// import { createConnectTransport as createConnectNodeTransport } from '@connectrpc/connect-node';
import { createConnectTransport as createConnectWebTransport } from '@connectrpc/connect-web';
import { createClient, type Client, Interceptor } from '@connectrpc/connect';

// extended to use interceptors
// https://connectrpc.com/docs/web/interceptors
// https://connectrpc.com/docs/node/interceptors/

const logger: Interceptor = next => async req => {
  const res = await next(req);
  if (res.stream) {
    // to intercept streaming response messages, we wrap
    // the AsynchronousIterable with a generator function
    return {
      ...res,
      message: logEach(res.message),
    };
  } else {
    console.log('Client Interceptor message:', res.message);
  }
  return res;
};

async function* logEach(stream: AsyncIterable<any>) {
  for await (const m of stream) {
    console.log('message received', m);
    yield m;
  }
}

export const useConnectWebRpc = <T extends DescService>(
  service: T,
  baseUrl: string,
  interceptors?: Interceptor[],
): Client<T> => {
  // expand input interceptors array adding in required interceptors
  const allInterceptors = (interceptors ?? []).push(logger);

  // This transport is going to be used throughout the app
  const transport = createConnectWebTransport({
    // httpVersion: '2',
    // httpVersion: '1.1',
    baseUrl: baseUrl,
  });
  // fs.writeFileSync(logPath, 'transport promise created');

  /**
   * Get a promise client for the given service.
   */

  // We memorize the client, so that we only create one instance per service.
  return useMemo(() => createClient(service, transport), [service]);
};
