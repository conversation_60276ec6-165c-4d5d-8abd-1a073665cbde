// import { z } from 'zod/v4';
// import { InventoryDbItemSchema } from '../commandSchemas';
// import { preprocessObjectId } from '../../preprocessFunctions';

// // An inventory transaction consists of the data describing the transaction,
// // and the item(s) that are being affected by the transaction
// // A transaction can be of one type only, add/remove/... you cannot add an item and remove another item in a single transaction
// // this MUST be an object not a reference for historical purposes
// export const LocationRouteSchema = z.object({
//   origin: preprocessObjectId.optional(), // InventoryStockLocation if a location is needed, origin would not be needed for location add/modify
//   destination: preprocessObjectId, // InventoryStockLocation if a location is needed, destination is always available
// });

// export const InventoryTransactionItemTransferSchema = InventoryDbItemSchema.extend({
//   transactionDataType: z.literal('Inventory.Items.Transfer'),
//   locationRoute: LocationRouteSchema,
// });
// export const InventoryTransactionItemIssueSchema = InventoryDbItemSchema.extend({
//   transactionDataType: z.literal('Inventory.Items.Issue'),
// });
// export const InventoryTransactionItemAdjustSchema = InventoryDbItemSchema.extend({
//   transactionDataType: z.literal('Inventory.Items.Adjust'),
// });
// export const InventoryTransactionItemSchema = InventoryDbItemSchema.extend({
//   transactionDataType: z.enum([
//     'Inventory.Items.Transfer',
//     'Inventory.Items.Issue',
//     'Inventory.Items.Adjust',
//   ]),
//   locationRoute: LocationRouteSchema.optional(),
// });
// // this is used for typing transactions and extracting data of any type of item transaction
// export type InventoryTransactionItem = z.infer<typeof InventoryTransactionItemSchema>;
