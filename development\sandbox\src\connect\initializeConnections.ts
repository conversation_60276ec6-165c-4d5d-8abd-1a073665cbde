import { ZodTypeAny } from 'zod/v4';
import { SchemaMapItemType } from '@tnt/zod-database-schemas';
import { MongoDBConnection } from '@tnt/mongo-client';
import { MongoClient, Db } from 'mongodb';
import { inspect } from 'node:util';
import { MONGO_DB_URI } from '../constants';

export async function intializeDatabaseConnectionInstances(
  serverURI: string,
  smis: Array<SchemaMapItemType<ZodTypeAny, string>>,
): Promise<{ status: boolean; failed: number }> {
  if (smis == null) {
    throw new Error('Error: Array<SchemaMapItemType> is null');
  }
  // await Promise.all(
  let failed = 0;

  for (let index = 0; index < smis.length; index++) {
    const smi = smis[index]!;
    const instance = MongoDBConnection.getInstance(smi);
    let db;
    try {
      db = await instance.connectDb(serverURI);
    } catch (error) {
      console.error('--------------ERROR-------------');
      console.dirxml(inspect(error));
      console.dirxml(inspect(smi));
      console.log(`Retrying dbConnect to ${smi.dbName}`);
      db = await instance.connectDb(serverURI);
    }

    const stats = await db.stats();
    console.log(`DbName: ${smi.dbName} stats`);
    console.dirxml(inspect(stats));

    //const col: Collection<T> = db.collection(smi.collectionName);
  }
  // )

  return { status: true, failed: failed };
}
