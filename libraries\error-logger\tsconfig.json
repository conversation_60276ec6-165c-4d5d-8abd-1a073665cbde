{
  "extends": "@tnt/typescript-config/base.json",
  "compilerOptions": {
    "allowJs": true,
    "declaration": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "forceConsistentCasingInFileNames": true,
    "lib": [
      "dom",
      "dom.iterable",
      "ESNext"
    ],
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "outDir": "./lib",
    "rootDir": "./src",
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "sourceMap": true,
    "strict": true,
    "target": "ESNext",
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
  ],
  "exclude": [
    "dist",
    "lib",
    "build",
    "node_modules",
  ]
}