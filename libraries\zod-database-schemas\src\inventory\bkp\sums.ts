import { z } from 'zod/v4';

import { preprocessObjectId, preprocessObjectId32 } from '../../common';
import { InventoryDbItemSchema } from '../commandSchemas/item';
import { ScheduledTransactionSchema } from '../commandSchemas/scheduledTransaction';

// Sum of items/products/... in a specific location (includes an array to determine status)
// cannot provide sums for individual status types since scheduled includes a date/time stamp
export const InventoryLocationSchema2 = z.object({
  //   _id: preprocessObjectId,
  active: z.boolean(), // used to deactivate old locations
  // itemLocation can be a project/container so that it is only available in that project/container (WIP)
  locationId: preprocessObjectId32, // z.union([InventoryLocationSchema(INV_LOCA), StructureLocationSchema (ROBJSOST, WDATSOST)]),
  //price: InventoryPriceSchema.optional(),
  // either-or? for lotNumber-serialNumber? both?
  items: z.array(InventoryDbItemSchema).optional(), // lot/serial numbers
  locationSum: z.number().readonly().optional(),
  scheduled: z.array(ScheduledTransactionSchema).optional(),
  threshold: z.number().optional(),
  target: z.number().optional(),
});

export const InventorySkuStockSchema2 = z.object({
  // _id: preprocessObjectId,
  active: z.boolean(), // used to deactivate old locations
  skuId: preprocessObjectId,
  sku: z.string(),
  items: z.array(InventoryDbItemSchema).optional(), // item _id
  skuSum: z.number().readonly().optional(),
  scheduled: z.array(ScheduledTransactionSchema).optional(),
  threshold: z.number().optional(),
  target: z.number().optional(),
});

export const InventoryProductStockSchema2 = z.object({
  // _id: preprocessObjectId,
  active: z.boolean(), // used to deactivate old locations
  productId: preprocessObjectId,
  skus: z.array(preprocessObjectId).optional(), // sku _id
  skuSum: z.number().readonly().optional(),
  scheduled: z.array(ScheduledTransactionSchema).optional(),
  threshold: z.number().optional(),
  target: z.number().optional(),
});

export const sum = z.object({
  _id: preprocessObjectId32,
  stockLocation: z.array(InventoryLocationSchema2),
  skuStock: z.array(InventorySkuStockSchema2),
  productStock: z.array(InventoryProductStockSchema2),
  locationId: preprocessObjectId32,

  xref: z.object({ childId: preprocessObjectId32, parentId: preprocessObjectId32 }),
});
