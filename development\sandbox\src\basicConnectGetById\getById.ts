import { ThemeSchema } from '@tnt/zod-database-schemas';
import { SchemaMap } from '@tnt/zod-database-schemas'; // SchemaMap is ONLY valid for dbase-schemas
import { baseROperations } from '@tnt/mongo-client';
import { z } from 'zod/v4';
import { inspect } from 'util';

// from powershell
// pnpm -F @tnt/sandbox exec tsx .\src\basicConnectGetById\getById.ts

type Theme = z.infer<typeof ThemeSchema>;

console.dir(inspect(await getById({ Id: 'APPSTHEM679683c3cc7905d32706e355' })));

console.log('.... process exiting');
process.exit(0); // Exit with code 0 (success)

export async function getById(req: { Id: string }): Promise<Theme> {
  const { Id } = req;

  // const idValidator = z.object({ _id: preprocessObjectId32 });
  // const { _id: id } = idValidator.parse({ _id: Id });

  // BRYAN this may be a better way to do a validation
  // but this should not be required, if it is a string it will be compared, if it fails, it will simply not find a matching record
  // and throw an error saying so
  const result = ThemeSchema.shape._id.safeParse(Id);
  // https://zod.dev/ERROR_HANDLING?id=error-handling-for-forms
  if (!result.success) {
    throw result.error;
  }
  const id = result.data;

  const themeOperations = new baseROperations<Theme>(SchemaMap.Application_Theme);

  const returnTheme = await themeOperations
    .getById(id, {
      info: {
        _id: 1,
        themeName: 1,
        layout: 1,
        overrides: 1,
        palette: 1,
        typography: 1,
        zones: 1,
      },
    })
    .catch(err => {
      throw err;
    });

  return returnTheme;
}
