import { z } from 'zod/v4';

import { StructureObjectWorkSchema } from '../../../../workData';

// omit items from ContainerWorkSchema and from ContainerBaseSchema that do not apply to a template
// since all work data is referenced from the work entry collections, really not much here.
export const StructureObjectWorkTemplateSchema = StructureObjectWorkSchema.extend({
  structureObjectType: z.literal('structureObjectWorkTemplate'),
});

export type StructureObjectWorkTemplate = z.infer<typeof StructureObjectWorkTemplateSchema>;
