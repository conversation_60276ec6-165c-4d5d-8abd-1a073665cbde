import { z } from 'zod/v4';

import { StructureObjectBaseSchema } from './base';

// This type of structure object is used to reference other structure objects
// It allows for reuse of structure components across different parts of a workflow
export const StructureObjectReferenceSourceSchema = StructureObjectBaseSchema.extend({
  structureObjectType: z.literal('structureObjectReferenceSource'),
}).pick({
  structureObjectType: true,
  _id: true,
  structureRootReference: true,
  structureReference: true,
  name: true,
  display: true,
  organizationalReferenceId: true,
});

export type StructureObjectReferenceSource = z.infer<typeof StructureObjectReferenceSourceSchema>;

// This type of structure object is used to reference other structure objects
// It allows for reuse of structure components across different parts of a workflow
export const StructureObjectReferenceTargetSchema = StructureObjectBaseSchema.extend({
  structureObjectType: z.literal('structureObjectReferenceTarget'),
}).pick({
  structureObjectType: true,
  _id: true,
  structureRootReference: true,
  structureReference: true,
  name: true,
  display: true,
  organizationalReferenceId: true,
});

export type StructureObjectReferenceTarget = z.infer<typeof StructureObjectReferenceTargetSchema>;
