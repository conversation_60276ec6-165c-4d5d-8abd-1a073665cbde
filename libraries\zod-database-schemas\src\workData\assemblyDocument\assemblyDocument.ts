import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';
import { AuthUserSchema } from '../../authentication';

import { AssemblyDocumentItemSchema } from './assemblyDocumentItem';

// if these get embedded in a structure rather than held in a separate database, what is the implications?
// easier to template, backup, export, archive. more difficult to update.
export const AssemblyDocumentSchema = z.object({
  _id: preprocessObjectId32,
  // assemblyDefType: z.enum(['bom', 'raf', 'checksheet', 'checklist']),
  // to track and link any WorkData entry we need to log the container it is associated with
  // and/or the contact/group/entity/.../resource who made the status entry
  structureRootReferenceId: preprocessObjectId32.optional().nullable().default(null), // this should be the main process/project
  structureReferenceId: preprocessObjectId32.optional().nullable().default(null), // this is the specific structure container in that main process/project
  assemblyDefName: z.string(),
  assemblyDefVersion: z.number().int(),
  validations: z.object({
    checkedBy: AuthUserSchema.optional().nullable().default(null),
    checkedDate: preprocessDate.optional().nullable().default(null),
    approvedBy: AuthUserSchema.optional().nullable().default(null),
    approvedDate: preprocessDate.optional().nullable().default(null),
    documentStatus: z.enum(['Complete-Passed', 'Complete-Failed', 'Incomplete', 'Not-Started']),
  }),
  embeddedResources: z.any().optional().nullable().default(null),
  // attachedResources: z.array(preprocessObjectId32),  // these should be attached by an IAMLink? not here?
  items: z
    .array(AssemblyDocumentItemSchema)
    .refine(
      items => items.length === 0 || items.every(i => i.assemblyDefType === items[0]?.assemblyDefType),
      { message: 'All items must have the same Assembly Document Type' },
    ),
});

export type AssemblyDocument = z.infer<typeof AssemblyDocumentSchema>;
