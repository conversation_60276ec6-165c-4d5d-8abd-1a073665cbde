import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../common';

export const ItemCrossReferenceEntrySchema = z.object({
  _id: preprocessObjectId32,
  itemId: preprocessObjectId32,
  skuId: preprocessObjectId32,
  productId: preprocessObjectId32,
  locationId: preprocessObjectId32,

  xref: z.object({ childId: preprocessObjectId32, parentId: preprocessObjectId32 }),
});

// Example entries

// childId is item, parentId is location
// childId is item, parentId is sku
// childId is location, parentId is sku
// childId is sku, parentId is product
// const entry1 = { xref: { childId: '4444', parentId: '5555' } };

// so finding by sku as childId would return all matched products (1)
// finding by location as childId would return all matched skus that are in that location
// finding by item would return one sku and all locations that item has ever been in ...

// how useful is this?

// typical use cases
// 1 creating a new sku (user created value!), must ensure that it is unique so checking the sku array in all products is required
//      this is probably not going to occur too often, and since sku is unique and an index, it should be relatively rapid for less that millions
//      rapid for less that millions of records
// 2 finding an item in inventory by serial/batch/itemId when you don't know the product or sku? How often can the end user not
//      start with a product/sku? it should be barcoded in! Is that a valid expectation?
// 3 retrieving all products/skus/items in a specific location, such as a project.
//      this is probably the most often use case and therefore problematic. can/should the inventory structure be modified for this?
//      should location sum be held in an array of item.sku.product in the location's collection?
//      or should there just be a sums collection?
