import { zDbase } from '@tnt/zod-database-schemas'; // imports all zod-database-schemas

import { ThemeSchema } from './application/dbAdmin'; // used the same file organization structure here
import { FormFieldTypes } from './types/zodFormsMeta';

import type { ZodTypeAny } from 'zod/v4';
import type { ZodFormsMeta } from './types/zodFormsMeta';
// import { SchemaMap } from './schemaMapping/schemaMap';  // can override functions also

// Define modifications (includes both classes and Zod objects)
// TODO M-1: kjm changed definition 20250513
// const modifications: Partial<Record<keyof typeof zDbase, any>> = {
const modifications: Partial<Record<keyof typeof zDbase, ZodTypeAny>> = {
  // SchemaMap, // Overridden class
  ThemeSchema, // Overridden Zod schema
};

// Dynamically extend only the specified items, objects, functions, ...
// eslint-disable-next-line @typescript-eslint/no-unused-vars  -- executed during startup?
const ZodQuerySchemas = Object.fromEntries(
  Object.entries(zDbase).map(([key, value]) => {
    if (key in modifications) {
      const modified = modifications[key as keyof typeof zDbase];
      // If modification is a function that extends a class, call it
      if (typeof modified === 'function' && Object.getPrototypeOf(modified) !== Object) {
        return [key, modified];
      }
      // Otherwise, assume it’s a direct replacement (Zod schema, object, etc.)
      return [key, modified];
    }
    return [key, value];
  }),
) as typeof zDbase;

// export * from '@tnt/zod-database-schemas'; // Export everything from the base package
// export only those schemas, types, ... needed for the clients
// should this follow the zod-database-schemas import/export paradigm?
export { ThemeSchema, type ZodFormsMeta, FormFieldTypes }; // export all overrides here, would include SchemaMap or other function name if applicable
