import { MongoClient } from 'mongodb';
import { Logger } from '@tnt/error-logger';

import type { ZodTypeAny } from 'zod/v4';
import type { SchemaMapItemType } from '@tnt/zod-database-schemas';
import type { Db } from 'mongodb';

// create a new instance/client connection for each database. When is the connection closed?

export class MongoDBConnection {
  private static instances: Map<string, MongoDBConnection> = new Map();
  private client: MongoClient | null = null;
  private db: Db | null = null;
  private smi: SchemaMapItemType<ZodTypeAny, string> | null = null;

  private constructor() {}
  logger = new Logger({ serviceName: '@tnt/mongo-client/dbConnection' });

  public static getInstance(smi: SchemaMapItemType<ZodTypeAny, string>): MongoDBConnection {
    if (!MongoDBConnection.instances.has(smi.dbName)) {
      MongoDBConnection.instances.set(smi.dbName, new MongoDBConnection());
    }
    const instance = MongoDBConnection.instances.get(smi.dbName)!;
    instance.smi = smi;
    // console.log(`dbConnection.getInstance complete: ` + smi.dbName);
    return instance;
  }

  // updated to capture db does not exist conditions as error
  public async connectDb(uri: string): Promise<Db> {
    try {
      if (!this.client) {
        this.client = new MongoClient(uri, { maxPoolSize: 100 });

        try {
          await this.client.connect();
        } catch (connectError) {
          console.error('[MongoDB] Connection error:', connectError);
          throw new Error('MongoClient failed to connect.');
        }
      }

      if (!this.smi) {
        throw new Error('SMI configuration is missing.');
      }

      if (!this.db) {
        const db = this.client.db(this.smi.dbName);

        try {
          await db.listCollections().toArray(); // Trigger validation
        } catch (err: unknown) {
          // console.error(`[MongoDB] Database "${this.smi.dbName}" does not exist or is inaccessible.`);
          this.logger.error(`MongoDB error: Cannot access database "${this.smi.dbName}",`, err);
          throw err;
        }
        this.db = db;
      }

      return this.db;
    } catch (err) {
      console.error('[MongoDB] connectDb failed:', err);
      throw err;
    }
  }

  public getDb(): Db | null {
    return this.db;
  }

  public getClient(): MongoClient | null {
    return this.client;
  }

  public isConnected(): boolean {
    return this.client !== null && this.db !== null;
  }
}
