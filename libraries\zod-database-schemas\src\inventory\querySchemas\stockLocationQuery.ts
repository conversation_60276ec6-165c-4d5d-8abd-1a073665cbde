import { z } from 'zod/v4';

import { preprocessObjectId, preprocessObjectId32 } from '../../common';
import { InventoryDbItemSchema } from '../commandSchemas/item';
import { ScheduledTransactionSchema } from '../commandSchemas/scheduledTransaction';

export const InventoryQueryStockLocationSchema = z.object({
  _id: preprocessObjectId,
  active: z.boolean(), // used to deactivate old locations
  // itemLocation can be a project/container so that it is only available in that project/container (WIP)
  locationId: preprocessObjectId32, // z.union([InventoryLocationSchema(INV_LOCA), StructureLocationSchema (ROBJSOST, WDATSOST)]),
  //price: InventoryPriceSchema.optional(),
  // either-or? for lotNumber-serialNumber? both?
  items: z.array(InventoryDbItemSchema).optional().nullable().default(null), // lot/serial numbers
  stockLocationSum: z.number().readonly().optional().nullable().default(null),
  scheduled: z.array(ScheduledTransactionSchema).optional().nullable().default(null),
  threshold: z.number().optional().nullable().default(null),
  target: z.number().optional().nullable().default(null),
});

export type InventoryQueryStockLocation = z.infer<typeof InventoryQueryStockLocationSchema>;
