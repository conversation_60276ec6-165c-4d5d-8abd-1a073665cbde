import { z } from 'zod/v4';
import { Logger } from '@tnt/error-logger';

import { MongoDBConnection } from '../clientConnections/dbConnection';
import { writeServerUriFromDotEnv } from '../utils/SchemaMapEnvLoad';

import type { SchemaMapItemType } from '@tnt/zod-database-schemas';
import type { ClientSession, Collection, Filter, MongoClient } from 'mongodb';
import type { ObjectId32 } from '@tnt/zod-database-schemas';

export type WithOptionalObjectId32<T extends { _id: ObjectId32 }> = Omit<T, '_id'> & {
  _id?: T['_id'];
};

// provides basic operations including db and schema functions
export class baseDbOperations<T extends { _id: ObjectId32 }> {
  protected _schemaMapItem: SchemaMapItemType<z.ZodTypeAny, string>;
  protected _schema: z.ZodTypeAny;

  logger = new Logger({ serviceName: '@tnt/mongo-client/baseDbOperations' });

  constructor(smi: SchemaMapItemType<z.ZodTypeAny, string>) {
    if (!smi.serverUri) {
      // load SchemaMap serverURIs from process.env
      writeServerUriFromDotEnv();
      // this.logger.debug(`Loading baseDbOperations, ${smi.referenceString} SchemaMapEnvLoad`);
    }
    if (!smi.serverUri) {
      this.logger.error(`Loading baseDbOperations, ${smi.referenceString} SchemaMapEnvLoad Failed`);
      throw new Error(
        `Error loading baseDbOperations,
        ${smi.referenceString} SchemaMapItemType.serverUri is null, SchemaMapEnvLoad Failed`,
      );
    }
    this._schemaMapItem = smi;
    this._schema = smi.schema;
  }

  // TODO M-1 update other functions with generic set?
  createFilter<U = T>(query: Partial<U>): Filter<U> {
    return query as Filter<U>;
  }

  async initCheckConnection(
    smi: SchemaMapItemType<z.ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<boolean> {
    const instance = MongoDBConnection.getInstance(smi);
    await instance.connectDb(this._schemaMapItem.serverUri);
    return instance.isConnected();
  }

  protected async getCollection(
    smi: SchemaMapItemType<z.ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<Collection<T>> {
    try {
      const instance = MongoDBConnection.getInstance(smi);
      const db = await instance.connectDb(this._schemaMapItem.serverUri);
      const col: Collection<T> = db.collection(smi.collectionName);
      return col;
    } catch (error: unknown) {
      this.logger.error(`Error baseCRUDOperations.getCollection, SchemaMapItem:`, error);
      console.dirxml(smi);
      throw error;
    }
  }

  protected async getClient(
    smi: SchemaMapItemType<z.ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<MongoClient | null> {
    try {
      const instance = MongoDBConnection.getInstance(smi);
      await instance.connectDb(this._schemaMapItem.serverUri);
      const client = instance.getClient();
      return client;
    } catch (error: unknown) {
      this.logger.error(`Error baseCRUDOperations.getClient, SchemaMapItem: ${JSON.stringify(smi)}`, error);
      throw error;
    }
  }

  // MongoDB transactions are only supported in replica sets or sharded clusters.
  // Make sure the MongoDB deployment supports transactions if planning to use them.
  // Use sessions judiciously, as transactions can introduce performance overhead and locking
  // https://stackoverflow.com/questions/60849686/use-mongoose-transactions-over-multiple-databases
  async transactionalOperation<R>(operations: (session: ClientSession) => Promise<R>): Promise<R> {
    const instance = MongoDBConnection.getInstance(this._schemaMapItem);
    // even though db is not being used, you HAVE to connect to get a session
    await instance.connectDb(this._schemaMapItem.serverUri);
    const client = instance.getClient()!;
    const session = client.startSession();

    try {
      session.startTransaction();
      const result = await operations(session); // Execute the operations within the transaction
      await session.commitTransaction();
      return result;
    } catch (error: unknown) {
      await session.abortTransaction();
      this.logger.error('Transaction failed:', error); // Log the error
      throw error;
    } finally {
      await session.endSession();
    }
  }

  public safeParse(
    data: unknown,
    smi: SchemaMapItemType<z.ZodTypeAny, string> = this._schemaMapItem,
  ): Partial<T> {
    let parseResult;
    const parsedDataType = Object.prototype.toString.call(data);
    try {
      if (parsedDataType === '[object]' || parsedDataType === '[object Object]') {
        parseResult = smi.schema.safeParse(data);
      } else if (parsedDataType === '[object Array]') {
        parseResult = z.array(smi.schema).safeParse(data);
      } else {
        this.logger.error(`baseRepository.CollectionOperations.parse unknown data type: ${parsedDataType}`);
        throw new Error(`baseRepository.CollectionOperations.parse unknown data type: ${parsedDataType}`);
      }
    } catch (error: unknown) {
      this.logger.error(`baseRepository.CollectionOperations.parse try_catch Error`, {
        error,
        dbName: smi.dbName,
        collectionName: smi.collectionName,
        // schemaShape: Object.keys(smi.schema.shape),
        data,
      });

      throw error;
    }
    if (!parseResult.success) {
      this.logger.error(`baseRepository.CollectionOperations.parse ZodError Issues:`, {
        dbName: smi.dbName,
        collectionName: smi.collectionName,
        zIssues: parseResult.error,
      });
      throw parseResult.error;
    }
    return parseResult.data as Partial<T>;
  }
}

// TypeError not handled by Zod parse or safeParse!? It throws an error and doesn't
// show as to where in the data or schema it occurs. PITA.
/*
  public parse(data: unknown): Partial<T> | z.ZodError {
    try {
      let validatedData;
      let parsedDataType = Object.prototype.toString.call(data);
      if (parsedDataType === '[object]' || parsedDataType === '[object Object]') {
        validatedData = this._schema.parse(data);
      } else if (parsedDataType === '[object Array]') {
        validatedData = z.array(this._schema).parse(data);
      } else {
        console.log('baseRepository.CollectionOperations.parse unknown data type: ' + parsedDataType);
      }
      return validatedData as Partial<T>;
    } catch (error) {
      console.error('parseFactory error type = ' + (error as Error).name);
      if (error instanceof Error && (error as Error).name == 'ZodError') {
        const err = error as z.ZodError;
        console.dirxml(err.issues);
        throw err;
      }
      if (error instanceof Error) {
        let message = 'baseRepository.parse Error:';
        if (error instanceof Error) {
          message = message + error.name + ': ' + error.message;
          console.error(message);
          throw error;
        }
      }
      throw error; // Stop execution if validation fails
    }
  }
*/
